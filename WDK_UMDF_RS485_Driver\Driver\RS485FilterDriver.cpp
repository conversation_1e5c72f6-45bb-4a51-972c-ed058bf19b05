//
// RS485 Filter Driver Implementation
// Windows User-Mode Driver Framework (UMDF) Implementation
// Main driver entry point and device management
//

#include "RS485FilterDriver.h"

//
// Driver Entry Point
//
NTSTATUS DriverEntry(
    _In_ PDRIVER_OBJECT DriverObject,
    _In_ PUNICODE_STRING RegistryPath
)
{
    NTSTATUS status;
    WDF_DRIVER_CONFIG config;
    WDF_OBJECT_ATTRIBUTES attributes;

    RS485_DEBUG_PRINT("RS485 Filter Driver Entry - Version %d.%d.%d.%d",
                      RS485_DRIVER_VERSION_MAJOR,
                      RS485_DRIVER_VERSION_MINOR,
                      RS485_DRIVER_VERSION_BUILD,
                      RS485_DRIVER_VERSION_REVISION);

    // Initialize driver configuration
    WDF_DRIVER_CONFIG_INIT(&config, RS485EvtDeviceAdd);
    config.EvtDriverUnload = RS485EvtDriverContextCleanup;

    // Initialize object attributes
    WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
    attributes.EvtCleanupCallback = RS485EvtDriverContextCleanup;

    // Create the driver object
    status = WdfDriverCreate(DriverObject,
                            RegistryPath,
                            &attributes,
                            &config,
                            WDF_NO_HANDLE);

    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("WdfDriverCreate failed: 0x%x", status);
        return status;
    }

    RS485_DEBUG_PRINT("RS485 Filter Driver initialized successfully");
    return STATUS_SUCCESS;
}

//
// Device Add Event Handler
//
NTSTATUS RS485EvtDeviceAdd(
    _In_ WDFDRIVER Driver,
    _Inout_ PWDFDEVICE_INIT DeviceInit
)
{
    NTSTATUS status;
    WDFDEVICE device;
    PRS485_DEVICE_CONTEXT deviceContext;
    WDF_OBJECT_ATTRIBUTES attributes;
    WDF_PNPPOWER_EVENT_CALLBACKS pnpPowerCallbacks;
    WDF_IO_QUEUE_CONFIG queueConfig;
    WDFQUEUE queue;

    UNREFERENCED_PARAMETER(Driver);

    RS485_DEBUG_PRINT("RS485EvtDeviceAdd called");

    // Configure as filter driver
    WdfFdoInitSetFilter(DeviceInit);

    // Set PnP and Power callbacks
    WDF_PNPPOWER_EVENT_CALLBACKS_INIT(&pnpPowerCallbacks);
    pnpPowerCallbacks.EvtDevicePrepareHardware = RS485EvtDevicePrepareHardware;
    pnpPowerCallbacks.EvtDeviceReleaseHardware = RS485EvtDeviceReleaseHardware;
    pnpPowerCallbacks.EvtDeviceD0Entry = RS485EvtDeviceD0Entry;
    pnpPowerCallbacks.EvtDeviceD0Exit = RS485EvtDeviceD0Exit;

    WdfDeviceInitSetPnpPowerEventCallbacks(DeviceInit, &pnpPowerCallbacks);

    // Initialize device attributes with context
    WDF_OBJECT_ATTRIBUTES_INIT_CONTEXT_TYPE(&attributes, RS485_DEVICE_CONTEXT);

    // Create the device object
    status = WdfDeviceCreate(&DeviceInit, &attributes, &device);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("WdfDeviceCreate failed: 0x%x", status);
        return status;
    }

    // Get device context
    deviceContext = RS485GetDeviceContext(device);
    RtlZeroMemory(deviceContext, sizeof(RS485_DEVICE_CONTEXT));
    deviceContext->Device = device;

    // Initialize synchronization objects
    WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
    status = WdfSpinLockCreate(&attributes, &deviceContext->BufferLock);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to create buffer spinlock: 0x%x", status);
        return status;
    }

    status = WdfSpinLockCreate(&attributes, &deviceContext->FrameLock);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to create frame spinlock: 0x%x", status);
        return status;
    }

    // Initialize buffer management
    status = RS485CreateBuffer(deviceContext, RS485_UPLINK_BUFFER_SLOTS, &deviceContext->UplinkBuffer);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to create uplink buffer: 0x%x", status);
        return status;
    }

    status = RS485CreateBuffer(deviceContext, RS485_DOWNLINK_BUFFER_SLOTS, &deviceContext->DownlinkBuffer);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to create downlink buffer: 0x%x", status);
        return status;
    }

    // Initialize frame processor
    status = RS485InitializeFrameProcessor(deviceContext);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to initialize frame processor: 0x%x", status);
        return status;
    }

    // Initialize configuration with defaults
    deviceContext->OverflowPolicy = BufferOverflowTriggerError;
    deviceContext->BufferThresholdPercent = RS485_BUFFER_THRESHOLD_DEFAULT;
    deviceContext->ResponseTimeoutMs = RS485_RESPONSE_TIMEOUT_MS;

    // Initialize statistics
    RS485InitializeErrorStatistics(&deviceContext->ErrorStatistics);
    RtlZeroMemory(&deviceContext->HardwareStatus, sizeof(RS485_HARDWARE_STATUS));
    RtlZeroMemory(&deviceContext->PerformanceMetrics, sizeof(RS485_PERFORMANCE_METRICS));

    // Create I/O queue for device control requests
    WDF_IO_QUEUE_CONFIG_INIT_DEFAULT_QUEUE(&queueConfig, WdfIoQueueDispatchParallel);
    queueConfig.EvtIoDeviceControl = RS485EvtIoDeviceControl;
    queueConfig.EvtIoStop = RS485EvtIoStop;

    WDF_OBJECT_ATTRIBUTES_INIT_CONTEXT_TYPE(&attributes, RS485_QUEUE_CONTEXT);

    status = WdfIoQueueCreate(device, &queueConfig, &attributes, &queue);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("WdfIoQueueCreate failed: 0x%x", status);
        return status;
    }

    // Initialize queue context
    PRS485_QUEUE_CONTEXT queueContext = RS485GetQueueContext(queue);
    queueContext->Queue = queue;
    queueContext->DeviceContext = deviceContext;

    // Create collection for pending requests
    WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
    status = WdfCollectionCreate(&attributes, &queueContext->PendingRequests);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to create pending requests collection: 0x%x", status);
        return status;
    }

    // Create spinlock for queue operations
    status = WdfSpinLockCreate(&attributes, &queueContext->QueueLock);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to create queue spinlock: 0x%x", status);
        return status;
    }

    // Create work items for asynchronous processing
    WDF_WORKITEM_CONFIG workItemConfig;
    WDF_WORKITEM_CONFIG_INIT(&workItemConfig, RS485EvtReceiveWorkItem);
    WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
    attributes.ParentObject = device;

    status = WdfWorkItemCreate(&workItemConfig, &attributes, &deviceContext->ReceiveWorkItem);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to create receive work item: 0x%x", status);
        return status;
    }

    WDF_WORKITEM_CONFIG_INIT(&workItemConfig, RS485EvtTransmitWorkItem);
    status = WdfWorkItemCreate(&workItemConfig, &attributes, &deviceContext->TransmitWorkItem);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to create transmit work item: 0x%x", status);
        return status;
    }

    // Create device interface
    status = WdfDeviceCreateDeviceInterface(device, &GUID_DEVINTERFACE_RS485_FILTER, NULL);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("WdfDeviceCreateDeviceInterface failed: 0x%x", status);
        return status;
    }

    RS485_DEBUG_PRINT("RS485 device added successfully");
    return STATUS_SUCCESS;
}

//
// Driver Context Cleanup
//
VOID RS485EvtDriverContextCleanup(
    _In_ WDFOBJECT DriverObject
)
{
    UNREFERENCED_PARAMETER(DriverObject);
    RS485_DEBUG_PRINT("RS485 Filter Driver cleanup");
}

//
// Device Prepare Hardware
//
NTSTATUS RS485EvtDevicePrepareHardware(
    _In_ WDFDEVICE Device,
    _In_ WDFCMRESLIST ResourcesRaw,
    _In_ WDFCMRESLIST ResourcesTranslated
)
{
    NTSTATUS status;
    PRS485_DEVICE_CONTEXT deviceContext;
    WDF_IO_TARGET_OPEN_PARAMS openParams;

    UNREFERENCED_PARAMETER(ResourcesRaw);
    UNREFERENCED_PARAMETER(ResourcesTranslated);

    deviceContext = RS485GetDeviceContext(Device);

    RS485_DEBUG_PRINT("RS485EvtDevicePrepareHardware called");

    // Get the lower device target (FTDI VCP driver)
    deviceContext->LowerDeviceTarget = WdfDeviceGetIoTarget(Device);

    // Open the lower device target
    WDF_IO_TARGET_OPEN_PARAMS_INIT_OPEN_BY_NAME(&openParams,
                                                NULL,
                                                GENERIC_READ | GENERIC_WRITE);

    status = WdfIoTargetOpen(deviceContext->LowerDeviceTarget, &openParams);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to open lower device target: 0x%x", status);
        return status;
    }

    // Initialize hardware status
    deviceContext->HardwareStatus.IsConnected = TRUE;
    deviceContext->HardwareStatus.FtdiChipStatus = 0;

    RS485_DEBUG_PRINT("Hardware prepared successfully");
    return STATUS_SUCCESS;
}

//
// Device Release Hardware
//
NTSTATUS RS485EvtDeviceReleaseHardware(
    _In_ WDFDEVICE Device,
    _In_ WDFCMRESLIST ResourcesTranslated
)
{
    PRS485_DEVICE_CONTEXT deviceContext;

    UNREFERENCED_PARAMETER(ResourcesTranslated);

    deviceContext = RS485GetDeviceContext(Device);

    RS485_DEBUG_PRINT("RS485EvtDeviceReleaseHardware called");

    // Close the lower device target
    if (deviceContext->LowerDeviceTarget != NULL) {
        WdfIoTargetClose(deviceContext->LowerDeviceTarget);
    }

    // Update hardware status
    deviceContext->HardwareStatus.IsConnected = FALSE;

    // Cleanup frame processor
    RS485CleanupFrameProcessor(deviceContext);

    // Cleanup buffers
    if (deviceContext->UplinkBuffer) {
        RS485DestroyBuffer(deviceContext->UplinkBuffer);
        deviceContext->UplinkBuffer = NULL;
    }

    if (deviceContext->DownlinkBuffer) {
        RS485DestroyBuffer(deviceContext->DownlinkBuffer);
        deviceContext->DownlinkBuffer = NULL;
    }

    RS485_DEBUG_PRINT("Hardware released successfully");
    return STATUS_SUCCESS;
}

//
// Device D0 Entry (Power Up)
//
NTSTATUS RS485EvtDeviceD0Entry(
    _In_ WDFDEVICE Device,
    _In_ WDF_POWER_DEVICE_STATE PreviousState
)
{
    PRS485_DEVICE_CONTEXT deviceContext;

    UNREFERENCED_PARAMETER(PreviousState);

    deviceContext = RS485GetDeviceContext(Device);

    RS485_DEBUG_PRINT("RS485EvtDeviceD0Entry called");

    // Reset buffer flags
    deviceContext->UplinkBufferFull = FALSE;
    deviceContext->DownlinkBufferFull = FALSE;

    // Clear buffers
    if (deviceContext->UplinkBuffer) {
        RS485ClearBuffer(deviceContext->UplinkBuffer);
    }
    if (deviceContext->DownlinkBuffer) {
        RS485ClearBuffer(deviceContext->DownlinkBuffer);
    }

    // Reset frame processor state
    deviceContext->FrameState = FrameStateWaitingHeader;
    deviceContext->BytesReceived = 0;
    RtlZeroMemory(&deviceContext->CurrentFrame, sizeof(RS485_FRAME));

    RS485_DEBUG_PRINT("Device entered D0 state");
    return STATUS_SUCCESS;
}

//
// Device D0 Exit (Power Down)
//
NTSTATUS RS485EvtDeviceD0Exit(
    _In_ WDFDEVICE Device,
    _In_ WDF_POWER_DEVICE_STATE TargetState
)
{
    PRS485_DEVICE_CONTEXT deviceContext;

    UNREFERENCED_PARAMETER(TargetState);

    deviceContext = RS485GetDeviceContext(Device);

    RS485_DEBUG_PRINT("RS485EvtDeviceD0Exit called");

    // Update hardware status
    deviceContext->HardwareStatus.IsConnected = FALSE;

    RS485_DEBUG_PRINT("Device exited D0 state");
    return STATUS_SUCCESS;
}
