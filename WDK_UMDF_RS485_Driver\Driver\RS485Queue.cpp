//
// RS485 I/O Queue Implementation
// Handles IOCTL requests and manages I/O operations
//

#include "RS485FilterDriver.h"

//
// I/O Device Control Event Handler
//
VOID RS485EvtIoDeviceControl(
    _In_ WDFQUEUE Queue,
    _In_ WDFREQUEST Request,
    _In_ size_t OutputBuffer<PERSON>ength,
    _In_ size_t InputBuffer<PERSON>ength,
    _In_ ULONG IoControlCode
)
{
    NTSTATUS status = STATUS_SUCCESS;
    PRS485_QUEUE_CONTEXT queueContext;
    PRS485_DEVICE_CONTEXT deviceContext;
    PRS485_REQUEST_CONTEXT requestContext;
    WDF_OBJECT_ATTRIBUTES attributes;

    queueContext = RS485GetQueueContext(Queue);
    deviceContext = queueContext->DeviceContext;

    RS485_DEBUG_PRINT("RS485EvtIoDeviceControl: IOCTL 0x%x, InputLen=%zu, OutputLen=%zu",
                      IoControlC<PERSON>, InputBuffer<PERSON>ength, OutputBufferLength);

    // Create request context
    WDF_OBJECT_ATTRIBUTES_INIT_CONTEXT_TYPE(&attributes, RS485_REQUEST_CONTEXT);
    status = WdfObjectAllocateContext(Request, &attributes, (PVOID*)&requestContext);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to allocate request context: 0x%x", status);
        WdfRequestComplete(Request, status);
        return;
    }

    // Initialize request context
    requestContext->Request = Request;
    requestContext->DeviceContext = deviceContext;
    requestContext->IOCTLCode = IoControlCode;
    requestContext->InputBufferLength = InputBufferLength;
    requestContext->OutputBufferLength = OutputBufferLength;
    KeQuerySystemTime(&requestContext->StartTime);

    // Get input and output buffers
    if (InputBufferLength > 0) {
        status = WdfRequestRetrieveInputBuffer(Request,
                                              InputBufferLength,
                                              &requestContext->InputBuffer,
                                              NULL);
        if (!NT_SUCCESS(status)) {
            RS485_ERROR_PRINT("Failed to retrieve input buffer: 0x%x", status);
            WdfRequestComplete(Request, status);
            return;
        }
    }

    if (OutputBufferLength > 0) {
        status = WdfRequestRetrieveOutputBuffer(Request,
                                               OutputBufferLength,
                                               &requestContext->OutputBuffer,
                                               NULL);
        if (!NT_SUCCESS(status)) {
            RS485_ERROR_PRINT("Failed to retrieve output buffer: 0x%x", status);
            WdfRequestComplete(Request, status);
            return;
        }
    }

    // Route IOCTL to appropriate handler
    switch (IoControlCode) {
        case IOCTL_RS485_CONFIGURE_SYSTEM:
            status = RS485HandleSystemConfigIOCTL(deviceContext, Request, InputBufferLength, OutputBufferLength);
            break;

        case IOCTL_RS485_CONFIGURE_USER:
            status = RS485HandleUserConfigIOCTL(deviceContext, Request, InputBufferLength, OutputBufferLength);
            break;

        case IOCTL_RS485_REQUEST_DATA:
            status = RS485HandleDataRequestIOCTL(deviceContext, Request, InputBufferLength, OutputBufferLength);
            break;

        case IOCTL_RS485_RECEIVE_RESPONSE:
            status = RS485HandleReceiveResponseIOCTL(deviceContext, Request, InputBufferLength, OutputBufferLength);
            break;

        case IOCTL_RS485_MODEL_DATA_OP:
            // Model data operations (W-series commands)
            status = STATUS_NOT_IMPLEMENTED; // TODO: Implement model data operations
            break;

        case IOCTL_RS485_GET_BUFFER_STATUS:
            status = RS485HandleBufferStatusIOCTL(deviceContext, Request, InputBufferLength, OutputBufferLength);
            break;

        case IOCTL_RS485_CLEAR_BUFFER:
            // Clear buffer operation
            status = STATUS_NOT_IMPLEMENTED; // TODO: Implement buffer clear
            break;

        case IOCTL_RS485_SET_BUFFER_POLICY:
            // Set buffer overflow policy
            status = STATUS_NOT_IMPLEMENTED; // TODO: Implement buffer policy
            break;

        case IOCTL_RS485_GET_HW_STATUS:
            // Get hardware status
            status = STATUS_NOT_IMPLEMENTED; // TODO: Implement hardware status
            break;

        case IOCTL_RS485_GET_PERFORMANCE:
            // Get performance metrics
            status = STATUS_NOT_IMPLEMENTED; // TODO: Implement performance metrics
            break;

        case IOCTL_RS485_CHECK_BUFFER_FLAGS:
            // Check buffer flags
            status = STATUS_NOT_IMPLEMENTED; // TODO: Implement buffer flag check
            break;

        default:
            RS485_WARNING_PRINT("Unsupported IOCTL code: 0x%x", IoControlCode);
            status = STATUS_INVALID_DEVICE_REQUEST;
            break;
    }

    // Complete request if not pending
    if (status != STATUS_PENDING) {
        WdfRequestComplete(Request, status);
    }
}

//
// I/O Stop Event Handler
//
VOID RS485EvtIoStop(
    _In_ WDFQUEUE Queue,
    _In_ WDFREQUEST Request,
    _In_ ULONG ActionFlags
)
{
    UNREFERENCED_PARAMETER(Queue);
    UNREFERENCED_PARAMETER(ActionFlags);

    RS485_DEBUG_PRINT("RS485EvtIoStop called");

    // Complete the request with STATUS_CANCELLED
    WdfRequestComplete(Request, STATUS_CANCELLED);
}

//
// System Configuration IOCTL Handler
//
NTSTATUS RS485HandleSystemConfigIOCTL(
    _In_ PRS485_DEVICE_CONTEXT DeviceContext,
    _In_ WDFREQUEST Request,
    _In_ size_t InputBufferLength,
    _In_ size_t OutputBufferLength
)
{
    NTSTATUS status;
    PRS485_SYSTEM_CONFIG_INPUT inputBuffer;
    RS485_FRAME frame;
    RS485_PAYLOAD payload;

    UNREFERENCED_PARAMETER(OutputBufferLength);

    // Validate input buffer size
    if (InputBufferLength < sizeof(RS485_SYSTEM_CONFIG_INPUT)) {
        RS485_ERROR_PRINT("Invalid input buffer size for system config: %zu", InputBufferLength);
        return STATUS_BUFFER_TOO_SMALL;
    }

    // Get input buffer
    status = WdfRequestRetrieveInputBuffer(Request,
                                          sizeof(RS485_SYSTEM_CONFIG_INPUT),
                                          (PVOID*)&inputBuffer,
                                          NULL);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to retrieve input buffer: 0x%x", status);
        return status;
    }

    // Validate command key
    if (!RS485_IS_SYSTEM_COMMAND(inputBuffer->CommandKey)) {
        RS485_ERROR_PRINT("Invalid system command key: %.4s", inputBuffer->CommandKey);
        return STATUS_INVALID_PARAMETER;
    }

    // Check uplink buffer availability
    BOOLEAN isBufferFull;
    status = RS485CheckBufferFlags(DeviceContext, BufferTypeUplink, &isBufferFull);
    if (!NT_SUCCESS(status)) {
        return status;
    }

    if (isBufferFull) {
        RS485_WARNING_PRINT("Uplink buffer is full, cannot send system config");
        return STATUS_DEVICE_BUSY;
    }

    // Build payload
    RS485StorePayloadKey(&payload, inputBuffer->CommandKey);
    
    // Store value based on command type
    if (strncmp(inputBuffer->CommandKey, "S001", 4) == 0) {
        // Slave address (1-31)
        UINT32 slaveAddress = (UINT32)(inputBuffer->Value & 0xFFFFFFFF);
        if (slaveAddress < RS485_MIN_SLAVE_ADDRESS || slaveAddress > RS485_MAX_SLAVE_ADDRESS) {
            RS485_ERROR_PRINT("Invalid slave address: %u", slaveAddress);
            return STATUS_INVALID_PARAMETER;
        }
        RS485StorePayloadInteger(&payload, slaveAddress);
    }
    else if (strncmp(inputBuffer->CommandKey, "S002", 4) == 0) {
        // Baud rate
        UINT32 baudRate = (UINT32)(inputBuffer->Value & 0xFFFFFFFF);
        UINT32 supportedRates[] = RS485_SUPPORTED_BAUD_RATES;
        BOOLEAN isValidBaudRate = FALSE;
        
        for (UINT32 i = 0; i < ARRAYSIZE(supportedRates); i++) {
            if (baudRate == supportedRates[i]) {
                isValidBaudRate = TRUE;
                break;
            }
        }
        
        if (!isValidBaudRate) {
            RS485_ERROR_PRINT("Invalid baud rate: %u", baudRate);
            return STATUS_INVALID_PARAMETER;
        }
        RS485StorePayloadInteger(&payload, baudRate);
    }

    // Build frame
    status = RS485BuildFrame(&payload,
                            RS485_FUNCTION_ASSIGN_DATA,
                            RS485_BROADCAST_ADDRESS,
                            &frame);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to build frame: 0x%x", status);
        return status;
    }

    // Add payload to uplink buffer
    status = RS485PushPayload(DeviceContext->UplinkBuffer, payload.Key);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to push payload to uplink buffer: 0x%x", status);
        return status;
    }

    // Send frame to lower driver
    status = RS485SendFrameToLowerDriver(DeviceContext, &frame);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to send frame to lower driver: 0x%x", status);
        return status;
    }

    // Update buffer flags
    RS485UpdateBufferFlags(DeviceContext);

    // Update performance metrics
    RS485UpdatePerformanceMetrics(DeviceContext, TRUE, 0);

    RS485_DEBUG_PRINT("System configuration sent successfully: %.4s", inputBuffer->CommandKey);
    return STATUS_SUCCESS;
}

//
// User Configuration IOCTL Handler
//
NTSTATUS RS485HandleUserConfigIOCTL(
    _In_ PRS485_DEVICE_CONTEXT DeviceContext,
    _In_ WDFREQUEST Request,
    _In_ size_t InputBufferLength,
    _In_ size_t OutputBufferLength
)
{
    NTSTATUS status;
    PRS485_USER_CONFIG_INPUT inputBuffer;
    RS485_FRAME frame;
    RS485_PAYLOAD payload;

    UNREFERENCED_PARAMETER(OutputBufferLength);

    // Validate input buffer size
    if (InputBufferLength < sizeof(RS485_USER_CONFIG_INPUT)) {
        RS485_ERROR_PRINT("Invalid input buffer size for user config: %zu", InputBufferLength);
        return STATUS_BUFFER_TOO_SMALL;
    }

    // Get input buffer
    status = WdfRequestRetrieveInputBuffer(Request,
                                          sizeof(RS485_USER_CONFIG_INPUT),
                                          (PVOID*)&inputBuffer,
                                          NULL);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to retrieve input buffer: 0x%x", status);
        return status;
    }

    // Validate command key
    if (!RS485_IS_USER_COMMAND(inputBuffer->CommandKey)) {
        RS485_ERROR_PRINT("Invalid user command key: %.4s", inputBuffer->CommandKey);
        return STATUS_INVALID_PARAMETER;
    }

    // Validate slave address
    if (!RS485_IS_VALID_SLAVE_ADDRESS(inputBuffer->SlaveAddress)) {
        RS485_ERROR_PRINT("Invalid slave address: %u", inputBuffer->SlaveAddress);
        return STATUS_INVALID_PARAMETER;
    }

    // Check uplink buffer availability
    BOOLEAN isBufferFull;
    status = RS485CheckBufferFlags(DeviceContext, BufferTypeUplink, &isBufferFull);
    if (!NT_SUCCESS(status)) {
        return status;
    }

    if (isBufferFull) {
        RS485_WARNING_PRINT("Uplink buffer is full, cannot send user config");
        return STATUS_DEVICE_BUSY;
    }

    // Build payload
    RS485StorePayloadKey(&payload, inputBuffer->CommandKey);
    
    // Store value based on command type (simplified for now)
    RS485StorePayloadInteger(&payload, (UINT32)(inputBuffer->Value & 0xFFFFFFFF));

    // Build frame
    status = RS485BuildFrame(&payload,
                            RS485_FUNCTION_ASSIGN_DATA,
                            inputBuffer->SlaveAddress,
                            &frame);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to build frame: 0x%x", status);
        return status;
    }

    // Add payload to uplink buffer
    status = RS485PushPayload(DeviceContext->UplinkBuffer, payload.Key);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to push payload to uplink buffer: 0x%x", status);
        return status;
    }

    // Send frame to lower driver
    status = RS485SendFrameToLowerDriver(DeviceContext, &frame);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to send frame to lower driver: 0x%x", status);
        return status;
    }

    // Update buffer flags
    RS485UpdateBufferFlags(DeviceContext);

    // Update performance metrics
    RS485UpdatePerformanceMetrics(DeviceContext, TRUE, 0);

    RS485_DEBUG_PRINT("User configuration sent successfully: %.4s to slave %u", 
                      inputBuffer->CommandKey, inputBuffer->SlaveAddress);
    return STATUS_SUCCESS;
}
