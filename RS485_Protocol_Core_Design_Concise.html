<!DOCTYPE html><html><head>
      <title>RS485_Protocol_Core_Design_Concise</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:///c:\Users\<USER>\.vscode\extensions\shd101wyy.markdown-preview-enhanced-0.8.18\crossnote\dependencies\katex\katex.min.css">
      
      
      
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:Consolas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h1 id="rs485-protocol-core-design---concise-overview">RS485 Protocol Core Design - Concise Overview </h1>
<h2 id="1-executive-summary">1. Executive Summary </h2>
<p><strong>Purpose</strong>: Windows RS485 driver for AI-SLDAP device communication using ZES protocol<br>
<strong>Architecture</strong>: Windows User-Mode Driver Framework (UMDF 2) filter driver with integrated FTDI VCP driver<br>
<strong>Deliverable</strong>: Single executable application (.exe) with complete RS485 communication solution<br>
<strong>Target</strong>: 32 devices maximum (30 slaves + 1 master) on RS485 bus topology<br>
<strong>Key Enhancement</strong>: Type-safe API with specific result types (ConfigurationResult, RequestResult, ResponseResult) for better error handling and semantic clarity</p>
<h2 id="2-system-architecture---three-layer-design">2. System Architecture - Three-Layer Design </h2>
<h3 id="21-layer-structure">2.1 Layer Structure </h3>
<pre data-role="codeBlock" data-info="" class="language-text"><code>┌─────────────────────────────────────────────────────────┐
│                 User Application Layer                  │
│              (Business Logic &amp; UI)                      │
└─────────────────────────────────────────────────────────┘
                            │
                    High-Level API Calls
                            │
┌─────────────────────────────────────────────────────────┐
│                RS485 Driver API Layer                   │
│        (5 API Categories + Management Functions)        │
│   • Error Handle API    • Master Broadcasting API       │
│   • Master Assign API   • Master Request API            │
│   • Slave Response API                                  │
└─────────────────────────────────────────────────────────┘
                            │
                   DeviceIoControl Interface
                            │
┌─────────────────────────────────────────────────────────┐
│            Windows UMDF 2 Driver Layer                  │
│    (Protocol Processing + FTDI VCP Integration)         │
│   • Frame Processing    • Buffer Management             │
│   • CRC Validation     • Address Filtering              │
│   • Function Code Routing                               │
└─────────────────────────────────────────────────────────┘
                            │
                      USB-RS485 Hardware
                            │
┌─────────────────────────────────────────────────────────┐
│                   Physical Layer                        │
│              RS485 Bus + FPGA Slaves                    │
└─────────────────────────────────────────────────────────┘
</code></pre><h3 id="22-data-flow-architecture">2.2 Data Flow Architecture </h3>
<p><strong>Frame Structure (16 bytes total)</strong>:</p>
<ul>
<li>Header(1) + ID(1) + <strong>Payload(12)</strong> + CRC(1) + Trailer(1)</li>
<li><strong>User API handles only 12-byte payload</strong>: Key(4) + Value(8)</li>
<li><strong>Driver handles protocol overhead</strong>: Header, ID, CRC, Trailer</li>
</ul>
<p><strong>Buffer Management</strong>:</p>
<ul>
<li><strong>Uplink Buffer</strong>: 5 payload slots × 12 bytes = 60 bytes total (PC to device)</li>
<li><strong>Downlink Buffer</strong>: 10 payload slots × 12 bytes = 120 bytes total (device to PC)</li>
<li><strong>Total Buffer Capacity</strong>: 180 bytes (pure payload data only)</li>
<li><strong>Payload-Centric Design</strong>: Buffers store only meaningful 12-byte payload data (Key + Value), not full 16-byte frames</li>
<li><strong>FIFO Processing</strong>: Strict First-In-First-Out ordering with sequence integrity verification</li>
<li><strong>Buffer Status Monitoring</strong>: Real-time buffer usage tracking with overflow prediction</li>
<li><strong>Overflow Protection</strong>: Configurable policies (discard oldest/newest, trigger error) with frame-by-frame checking</li>
</ul>
<h2 id="3-api-categories-and-function-code-mapping">3. API Categories and Function Code Mapping </h2>
<table>
<thead>
<tr>
<th style="text-align:center">Function Code</th>
<th style="text-align:center">Binary</th>
<th style="text-align:left">API Category</th>
<th style="text-align:left">Description</th>
<th style="text-align:left">Used By</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:center"><strong>0b111</strong></td>
<td style="text-align:center">0x07</td>
<td style="text-align:left">Broadcasting + Assign Data</td>
<td style="text-align:left">System config + User config</td>
<td style="text-align:left">Master</td>
</tr>
<tr>
<td style="text-align:center"><strong>0b110</strong></td>
<td style="text-align:center">0x06</td>
<td style="text-align:left">Master Request</td>
<td style="text-align:left">Data queries</td>
<td style="text-align:left">Master</td>
</tr>
<tr>
<td style="text-align:center"><strong>0b010</strong></td>
<td style="text-align:center">0x02</td>
<td style="text-align:left">Slave Response</td>
<td style="text-align:left">Assign acknowledgments</td>
<td style="text-align:left">Slave</td>
</tr>
<tr>
<td style="text-align:center"><strong>0b001</strong></td>
<td style="text-align:center">0x01</td>
<td style="text-align:left">Slave Response</td>
<td style="text-align:left">Data responses</td>
<td style="text-align:left">Slave</td>
</tr>
<tr>
<td style="text-align:center"><strong>0b000</strong></td>
<td style="text-align:center">0x00</td>
<td style="text-align:left">Error Handle</td>
<td style="text-align:left">Retry mechanism</td>
<td style="text-align:left">Both</td>
</tr>
</tbody>
</table>
<h2 id="4-implementation-priority-and-complete-api-reference">4. Implementation Priority and Complete API Reference </h2>
<h3 id="41-first-priority---system-configuration-s-series">4.1 FIRST PRIORITY - System Configuration (S-series) </h3>
<table>
<thead>
<tr>
<th>Command</th>
<th>Description</th>
<th>Value Range</th>
<th>API Call</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>S001</strong></td>
<td>Set RS485 slave address</td>
<td>1-31</td>
<td><code>configureSystemSettings('S001', 5)</code></td>
</tr>
<tr>
<td><strong>S002</strong></td>
<td>Set baud rate</td>
<td>9600, 19200, 38400, 57600, 115200</td>
<td><code>configureSystemSettings('S002', 115200)</code></td>
</tr>
</tbody>
</table>
<p><strong>S-series Usage Examples:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Step 1: Set slave address (broadcast to default address 0x00)</span>
<span class="token comment">// IMPORTANT: Only one slave should be connected during address assignment</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> targetSlaveAddress <span class="token operator">=</span> <span class="token number">5</span><span class="token punctuation">;</span>  <span class="token comment">// Store the address for subsequent commands</span>
driver<span class="token punctuation">.</span><span class="token function">configureSystemSettings</span><span class="token punctuation">(</span><span class="token string">"S001"</span><span class="token punctuation">,</span> targetSlaveAddress<span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Set address to 5</span>

<span class="token comment">// Step 2: Set communication baud rate</span>
driver<span class="token punctuation">.</span><span class="token function">configureSystemSettings</span><span class="token punctuation">(</span><span class="token string">"S002"</span><span class="token punctuation">,</span> <span class="token number">115200</span><span class="token punctuation">)</span><span class="token punctuation">;</span>   <span class="token comment">// Set to 115200 bps</span>

<span class="token comment">// Step 3: Verify configuration - Use the address we just set</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
ResponseResult result <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>targetSlaveAddress<span class="token punctuation">,</span> responseData<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Configuration Verification - What PC Receives:</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>result <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>string key <span class="token operator">=</span> <span class="token function">extractKey</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> confirmedValue <span class="token operator">=</span> <span class="token function">extractInteger</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>key <span class="token operator">==</span> <span class="token string">"S001"</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"✓ S001 Configuration SUCCESSFUL - Address confirmed: "</span> <span class="token operator">&lt;&lt;</span> confirmedValue <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
        <span class="token comment">// Expected: confirmedValue = targetSlaveAddress (the address we just set)</span>
    <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>key <span class="token operator">==</span> <span class="token string">"S002"</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"✓ S002 Configuration SUCCESSFUL - Baud rate confirmed: "</span> <span class="token operator">&lt;&lt;</span> confirmedValue <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
        <span class="token comment">// Expected: confirmedValue = 115200 (the baud rate we just set)</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>result <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>TIMEOUT<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"✗ Configuration FAILED - No response from slave"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Possible causes:"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"  - Slave device not connected or powered off"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"  - Wrong baud rate (for S002 command)"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"  - Multiple slaves connected (for S001 command)"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Action: Check hardware connections and retry"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
<span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>result <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>CRC_ERROR<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"✗ Configuration FAILED - Data corruption detected"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Possible causes:"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"  - Electrical interference on RS485 bus"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"  - Poor cable connections or damaged cable"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Action: Check cable integrity and retry"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
<span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>result <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>INVALID_ADDRESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"✗ Configuration FAILED - Address conflict or invalid"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Possible causes:"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"  - Multiple slaves with same address (for S001)"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"  - Address out of valid range (1-31)"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Action: Ensure only one slave connected for S001, check address range"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h3 id="42-second-priority---user-configuration-u-series">4.2 SECOND PRIORITY - User Configuration (U-series) </h3>
<table>
<thead>
<tr>
<th>Command</th>
<th>Description</th>
<th>Value Range</th>
<th>API Call</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>U001</strong></td>
<td>SEL detection threshold</td>
<td>40-500 milliampere</td>
<td><code>configureUserSettings('U001', 250)</code></td>
</tr>
<tr>
<td><strong>U002</strong></td>
<td>SEL maximum amplitude threshold</td>
<td>1000-2000 milliampere</td>
<td><code>configureUserSettings('U002', 1500)</code></td>
</tr>
<tr>
<td><strong>U003</strong></td>
<td>SEL detection count before power cycle</td>
<td>1-5</td>
<td><code>configureUserSettings('U003', 3)</code></td>
</tr>
<tr>
<td><strong>U004</strong></td>
<td>Power cycle duration</td>
<td>200,400,600,800,1000 ms</td>
<td><code>configureUserSettings('U004', 600)</code></td>
</tr>
<tr>
<td><strong>U005</strong></td>
<td>GPIO input channel control</td>
<td>Channel + Enable flag</td>
<td><code>configureUserSettings('U005', channelData)</code></td>
</tr>
<tr>
<td><strong>U006</strong></td>
<td>GPIO output channel control</td>
<td>Channel + Enable flag</td>
<td><code>configureUserSettings('U006', channelData)</code></td>
</tr>
</tbody>
</table>
<p><strong>U-series Usage Examples:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Configure SEL detection parameters</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U001"</span><span class="token punctuation">,</span> <span class="token number">250</span><span class="token punctuation">)</span><span class="token punctuation">;</span>    <span class="token comment">// SEL threshold: 250mA</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U002"</span><span class="token punctuation">,</span> <span class="token number">1500</span><span class="token punctuation">)</span><span class="token punctuation">;</span>   <span class="token comment">// Max amplitude: 1500mA</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U003"</span><span class="token punctuation">,</span> <span class="token number">3</span><span class="token punctuation">)</span><span class="token punctuation">;</span>      <span class="token comment">// 3 detections before power cycle</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U004"</span><span class="token punctuation">,</span> <span class="token number">600</span><span class="token punctuation">)</span><span class="token punctuation">;</span>    <span class="token comment">// 600ms power cycle duration</span>

<span class="token comment">// Configure GPIO channels - Complete Examples</span>
<span class="token comment">// Enable GPIO input ch0</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U005"</span><span class="token punctuation">,</span> <span class="token number">0x100000000ULL</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// Channel=0, Enable=1</span>
<span class="token comment">// Enable GPIO input ch1</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U005"</span><span class="token punctuation">,</span> <span class="token number">0x100000001ULL</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// Channel=1, Enable=1</span>
<span class="token comment">// Enable GPIO output ch0</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U006"</span><span class="token punctuation">,</span> <span class="token number">0x100000000ULL</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// Channel=0, Enable=1</span>
<span class="token comment">// Enable GPIO output ch1</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U006"</span><span class="token punctuation">,</span> <span class="token number">0x100000001ULL</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// Channel=1, Enable=1</span>

<span class="token comment">// Disable GPIO channels - Complete Examples</span>
<span class="token comment">// Disable GPIO input ch0</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U005"</span><span class="token punctuation">,</span> <span class="token number">0x000000000ULL</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// Channel=0, Enable=0</span>
<span class="token comment">// Disable GPIO input ch1</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U005"</span><span class="token punctuation">,</span> <span class="token number">0x000000001ULL</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// Channel=1, Enable=0</span>
<span class="token comment">// Disable GPIO output ch0</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U006"</span><span class="token punctuation">,</span> <span class="token number">0x000000000ULL</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// Channel=0, Enable=0</span>
<span class="token comment">// Disable GPIO output ch1</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U006"</span><span class="token punctuation">,</span> <span class="token number">0x000000001ULL</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// Channel=1, Enable=0</span>

<span class="token comment">// Receive acknowledgment for each configuration - Use address from S001</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> ackData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>targetSlaveAddress<span class="token punctuation">,</span> ackData<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><p><strong>GPIO Value Packing for U005/U006:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Format: Lower 32 bits = Channel ID, Upper 32 bits = Enable Flag</span>
<span class="token keyword keyword-uint64_t">uint64_t</span> gpioValue <span class="token operator">=</span> <span class="token punctuation">(</span><span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-uint64_t">uint64_t</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>enableFlag<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">32</span><span class="token punctuation">)</span> <span class="token operator">|</span> channelId<span class="token punctuation">;</span>

<span class="token comment">// Complete Examples for All GPIO Operations:</span>
<span class="token comment">// Enable GPIO input channel 0:  configureUserSettings('U005', 0x100000000ULL)</span>
<span class="token comment">// Enable GPIO input channel 1:  configureUserSettings('U005', 0x100000001ULL)</span>
<span class="token comment">// Disable GPIO input channel 0: configureUserSettings('U005', 0x000000000ULL)</span>
<span class="token comment">// Disable GPIO input channel 1: configureUserSettings('U005', 0x000000001ULL)</span>

<span class="token comment">// Enable GPIO output channel 0:  configureUserSettings('U006', 0x100000000ULL)</span>
<span class="token comment">// Enable GPIO output channel 1:  configureUserSettings('U006', 0x100000001ULL)</span>
<span class="token comment">// Disable GPIO output channel 0: configureUserSettings('U006', 0x000000000ULL)</span>
<span class="token comment">// Disable GPIO output channel 1: configureUserSettings('U006', 0x000000001ULL)</span>

<span class="token comment">// Value Calculation Examples:</span>
<span class="token comment">// Channel 0, Enable:  (1 &lt;&lt; 32) | 0 = 0x100000000ULL</span>
<span class="token comment">// Channel 1, Enable:  (1 &lt;&lt; 32) | 1 = 0x100000001ULL</span>
<span class="token comment">// Channel 0, Disable: (0 &lt;&lt; 32) | 0 = 0x000000000ULL</span>
<span class="token comment">// Channel 1, Disable: (0 &lt;&lt; 32) | 1 = 0x000000001ULL</span>
</code></pre><h3 id="43-third-priority---application-data-queries-a-series">4.3 THIRD PRIORITY - Application Data Queries (A-series) </h3>
<table>
<thead>
<tr>
<th>Command</th>
<th>Description</th>
<th>Response Data</th>
<th>API Call</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>A001</strong></td>
<td>Request SEL event log</td>
<td>JSON structure with event records</td>
<td><code>requestData('A001')</code></td>
</tr>
<tr>
<td><strong>A002</strong></td>
<td>Request device status</td>
<td>Status flags (16-bit)</td>
<td><code>requestData('A002')</code></td>
</tr>
<tr>
<td><strong>A003</strong></td>
<td>Request firmware version</td>
<td>Version string</td>
<td><code>requestData('A003')</code></td>
</tr>
<tr>
<td><strong>A004</strong></td>
<td>Request system statistics</td>
<td>JSON structure with statistics</td>
<td><code>requestData('A004')</code></td>
</tr>
<tr>
<td><strong>A005</strong></td>
<td>Request current configuration</td>
<td>JSON structure with all current settings</td>
<td><code>requestData('A005')</code></td>
</tr>
</tbody>
</table>
<p><strong>A-series Usage Examples:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Request data (non-blocking design)</span>
driver<span class="token punctuation">.</span><span class="token function">requestData</span><span class="token punctuation">(</span><span class="token string">"A001"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Request SEL event log</span>
driver<span class="token punctuation">.</span><span class="token function">requestData</span><span class="token punctuation">(</span><span class="token string">"A002"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Request device status</span>
driver<span class="token punctuation">.</span><span class="token function">requestData</span><span class="token punctuation">(</span><span class="token string">"A003"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Request firmware version</span>
driver<span class="token punctuation">.</span><span class="token function">requestData</span><span class="token punctuation">(</span><span class="token string">"A004"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Request system statistics</span>
driver<span class="token punctuation">.</span><span class="token function">requestData</span><span class="token punctuation">(</span><span class="token string">"A005"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Request current configuration</span>

<span class="token comment">// Receive responses for each request - Use address from S001</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> eventData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">,</span> statusData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">,</span> versionData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>targetSlaveAddress<span class="token punctuation">,</span> eventData<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>targetSlaveAddress<span class="token punctuation">,</span> statusData<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>targetSlaveAddress<span class="token punctuation">,</span> versionData<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Process received data</span>
std<span class="token double-colon punctuation">::</span>string eventKey <span class="token operator">=</span> <span class="token function">extractKey</span><span class="token punctuation">(</span>eventData<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>eventKey <span class="token operator">==</span> <span class="token string">"A001"</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// Process SEL event log JSON data</span>
    <span class="token keyword keyword-uint64_t">uint64_t</span> jsonDataPointer <span class="token operator">=</span> <span class="token function">extractDouble</span><span class="token punctuation">(</span>eventData<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h3 id="44-fourth-priority---model-data-operations-w-series">4.4 FOURTH PRIORITY - Model Data Operations (W-series) </h3>
<table>
<thead>
<tr>
<th>Command</th>
<th>Description</th>
<th>Parameters</th>
<th>API Call</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>W001</strong></td>
<td>Write model data to FRAM</td>
<td>Memory address, data bytes</td>
<td><code>modelDataOperation(address, data, true, length)</code></td>
</tr>
<tr>
<td><strong>W002</strong></td>
<td>Read model data from FRAM</td>
<td>Memory address, length</td>
<td><code>modelDataOperation(address, data, false, length)</code></td>
</tr>
</tbody>
</table>
<p><strong>W-series Usage Examples:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Write AI model weights to FRAM</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> weightData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">{</span><span class="token comment">/* weight data */</span><span class="token punctuation">}</span><span class="token punctuation">;</span>
driver<span class="token punctuation">.</span><span class="token function">modelDataOperation</span><span class="token punctuation">(</span><span class="token number">0x1000</span><span class="token punctuation">,</span> weightData<span class="token punctuation">,</span> <span class="token boolean">true</span><span class="token punctuation">,</span> <span class="token number">12</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Write to address 0x1000</span>

<span class="token comment">// Read AI model bias from FRAM</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> biasData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
driver<span class="token punctuation">.</span><span class="token function">modelDataOperation</span><span class="token punctuation">(</span><span class="token number">0x2000</span><span class="token punctuation">,</span> biasData<span class="token punctuation">,</span> <span class="token boolean">false</span><span class="token punctuation">,</span> <span class="token number">12</span><span class="token punctuation">)</span><span class="token punctuation">;</span>   <span class="token comment">// Read from address 0x2000</span>

<span class="token comment">// Receive acknowledgment/response - Use address from S001</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>targetSlaveAddress<span class="token punctuation">,</span> responseData<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h3 id="45-fifth-priority---error-handling-and-management-apis">4.5 FIFTH PRIORITY - Error Handling and Management APIs </h3>
<p><strong>Management APIs (FTDI-Style):</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Port Management</span>
ConnectionResult <span class="token function">openPort</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> portName<span class="token punctuation">)</span><span class="token punctuation">;</span>
ConnectionResult <span class="token function">closePort</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-bool">bool</span> <span class="token function">isPortOpen</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span><span class="token punctuation">;</span>
PortResult <span class="token function">getPortInfo</span><span class="token punctuation">(</span>PortInfo<span class="token operator">&amp;</span> info<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Device Discovery</span>
<span class="token keyword keyword-static">static</span> EnumerationResult <span class="token function">enumerateDevices</span><span class="token punctuation">(</span>std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span>DeviceInfo<span class="token operator">&gt;</span><span class="token operator">&amp;</span> deviceList<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-static">static</span> DetectionResult <span class="token function">detectMultipleDevices</span><span class="token punctuation">(</span>std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span><span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">&gt;</span><span class="token operator">&amp;</span> detectedAddresses<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Buffer Management (Mandatory before data transmission)</span>
BufferResult <span class="token function">getBufferStatus</span><span class="token punctuation">(</span>BufferStatus<span class="token operator">&amp;</span> status<span class="token punctuation">)</span><span class="token punctuation">;</span>
BufferResult <span class="token function">checkUplinkBufferAvailability</span><span class="token punctuation">(</span><span class="token keyword keyword-bool">bool</span><span class="token operator">&amp;</span> isFull<span class="token punctuation">)</span><span class="token punctuation">;</span>
BufferResult <span class="token function">checkDownlinkBufferAvailability</span><span class="token punctuation">(</span><span class="token keyword keyword-bool">bool</span><span class="token operator">&amp;</span> isFull<span class="token punctuation">)</span><span class="token punctuation">;</span>
BufferResult <span class="token function">clearBuffer</span><span class="token punctuation">(</span>BufferType bufferType <span class="token operator">=</span> BufferType<span class="token double-colon punctuation">::</span>BOTH<span class="token punctuation">)</span><span class="token punctuation">;</span>
BufferResult <span class="token function">setBufferOverflowPolicy</span><span class="token punctuation">(</span>BufferOverflowPolicy policy<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// BufferStatus structure for detailed buffer monitoring</span>
<span class="token keyword keyword-struct">struct</span> <span class="token class-name">BufferStatus</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> uplinkUsed<span class="token punctuation">;</span>        <span class="token comment">// Used payload slots in uplink buffer (0-5)</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> uplinkTotal<span class="token punctuation">;</span>       <span class="token comment">// Total uplink buffer capacity (5 payload slots)</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> downlinkUsed<span class="token punctuation">;</span>      <span class="token comment">// Used payload slots in downlink buffer (0-10)</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> downlinkTotal<span class="token punctuation">;</span>     <span class="token comment">// Total downlink buffer capacity (10 payload slots)</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> payloadSize<span class="token punctuation">;</span>       <span class="token comment">// Size per payload slot (12 bytes)</span>
    <span class="token keyword keyword-bool">bool</span> isUplinkFull<span class="token punctuation">;</span>          <span class="token comment">// Uplink buffer full flag</span>
    <span class="token keyword keyword-bool">bool</span> isDownlinkFull<span class="token punctuation">;</span>        <span class="token comment">// Downlink buffer full flag</span>
    <span class="token keyword keyword-bool">bool</span> isOverflowDetected<span class="token punctuation">;</span>    <span class="token comment">// Buffer overflow status</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> totalBufferBytes<span class="token punctuation">;</span>  <span class="token comment">// Total buffer capacity in bytes (180 bytes)</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Hardware Status and Performance</span>
HardwareResult <span class="token function">getHardwareStatus</span><span class="token punctuation">(</span>HardwareStatus<span class="token operator">&amp;</span> status<span class="token punctuation">)</span><span class="token punctuation">;</span>
PerformanceResult <span class="token function">getPerformanceMetrics</span><span class="token punctuation">(</span>PerformanceMetrics<span class="token operator">&amp;</span> metrics<span class="token punctuation">)</span><span class="token punctuation">;</span>
LineResult <span class="token function">getLineStatus</span><span class="token punctuation">(</span>LineStatus<span class="token operator">&amp;</span> status<span class="token punctuation">)</span><span class="token punctuation">;</span>
ConfigResult <span class="token function">getBaudRate</span><span class="token punctuation">(</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&amp;</span> currentBaudRate<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Error Handling</span>
std<span class="token double-colon punctuation">::</span>string <span class="token function">getErrorString</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> ConnectionResult<span class="token operator">&amp;</span> error<span class="token punctuation">)</span><span class="token punctuation">;</span>
std<span class="token double-colon punctuation">::</span>string <span class="token function">getErrorString</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> BufferResult<span class="token operator">&amp;</span> error<span class="token punctuation">)</span><span class="token punctuation">;</span>
std<span class="token double-colon punctuation">::</span>string <span class="token function">getErrorString</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> ConfigurationResult<span class="token operator">&amp;</span> error<span class="token punctuation">)</span><span class="token punctuation">;</span>
std<span class="token double-colon punctuation">::</span>string <span class="token function">getErrorString</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> RequestResult<span class="token operator">&amp;</span> error<span class="token punctuation">)</span><span class="token punctuation">;</span>
std<span class="token double-colon punctuation">::</span>string <span class="token function">getErrorString</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> ResponseResult<span class="token operator">&amp;</span> error<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><p><strong>Specific Result Types for Enhanced Type Safety:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Different result types for different operation categories - replaces generic RS485Error</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">ConnectionResult</span> <span class="token punctuation">{</span> SUCCESS<span class="token punctuation">,</span> PORT_NOT_FOUND<span class="token punctuation">,</span> ACCESS_DENIED<span class="token punctuation">,</span> ALREADY_OPEN<span class="token punctuation">,</span> CONNECTION_ERROR<span class="token punctuation">,</span> DEVICE_NOT_FOUND <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">BufferResult</span> <span class="token punctuation">{</span> SUCCESS<span class="token punctuation">,</span> INSUFFICIENT_BUFFER<span class="token punctuation">,</span> BUFFER_OVERFLOW<span class="token punctuation">,</span> INVALID_BUFFER_TYPE<span class="token punctuation">,</span> BUFFER_FULL <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">ConfigurationResult</span> <span class="token punctuation">{</span> SUCCESS<span class="token punctuation">,</span> INVALID_PARAMETER<span class="token punctuation">,</span> DEVICE_NOT_RESPONDING<span class="token punctuation">,</span> CRC_ERROR<span class="token punctuation">,</span> TIMEOUT_ERROR<span class="token punctuation">,</span> PROTOCOL_ERROR <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">RequestResult</span> <span class="token punctuation">{</span> SUCCESS<span class="token punctuation">,</span> INVALID_COMMAND<span class="token punctuation">,</span> BUFFER_FULL<span class="token punctuation">,</span> TIMEOUT_ERROR<span class="token punctuation">,</span> DEVICE_BUSY <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">ResponseResult</span> <span class="token punctuation">{</span> SUCCESS<span class="token punctuation">,</span> TIMEOUT_ERROR<span class="token punctuation">,</span> CRC_ERROR<span class="token punctuation">,</span> INVALID_ADDRESS<span class="token punctuation">,</span> NO_DATA_AVAILABLE <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">HardwareResult</span> <span class="token punctuation">{</span> SUCCESS<span class="token punctuation">,</span> HARDWARE_ERROR<span class="token punctuation">,</span> DRIVER_ERROR <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">PerformanceResult</span> <span class="token punctuation">{</span> SUCCESS<span class="token punctuation">,</span> METRICS_UNAVAILABLE <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">LineResult</span> <span class="token punctuation">{</span> SUCCESS<span class="token punctuation">,</span> LINE_ERROR <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">EnumerationResult</span> <span class="token punctuation">{</span> SUCCESS<span class="token punctuation">,</span> NO_DEVICES_FOUND<span class="token punctuation">,</span> ENUMERATION_ERROR <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">DetectionResult</span> <span class="token punctuation">{</span> SUCCESS<span class="token punctuation">,</span> MULTIPLE_DEVICES<span class="token punctuation">,</span> NO_RESPONSE <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">ModelDataResult</span> <span class="token punctuation">{</span> SUCCESS<span class="token punctuation">,</span> MEMORY_ERROR<span class="token punctuation">,</span> INVALID_ADDRESS<span class="token punctuation">,</span> DATA_CORRUPTION <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">VerificationResult</span> <span class="token punctuation">{</span> SUCCESS<span class="token punctuation">,</span> VALUE_MISMATCH<span class="token punctuation">,</span> VERIFICATION_FAILED <span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><p><strong>Error Handling Usage Examples:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Complete error handling workflow with specific result types</span>
ConnectionResult portResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">openPort</span><span class="token punctuation">(</span><span class="token string">"COM3"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>portResult <span class="token operator">!=</span> ConnectionResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Port Error: "</span> <span class="token operator">&lt;&lt;</span> driver<span class="token punctuation">.</span><span class="token function">getErrorString</span><span class="token punctuation">(</span>portResult<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    <span class="token keyword keyword-return">return</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// Buffer status checking before transmission</span>
BufferStatus status<span class="token punctuation">;</span>
BufferResult bufferResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">getBufferStatus</span><span class="token punctuation">(</span>status<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>bufferResult <span class="token operator">!=</span> BufferResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Buffer Error: "</span> <span class="token operator">&lt;&lt;</span> driver<span class="token punctuation">.</span><span class="token function">getErrorString</span><span class="token punctuation">(</span>bufferResult<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// Configuration with error handling</span>
ConfigurationResult configResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">configureSystemSettings</span><span class="token punctuation">(</span><span class="token string">"S001"</span><span class="token punctuation">,</span> <span class="token number">5</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>configResult <span class="token operator">!=</span> ConfigurationResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Config Error: "</span> <span class="token operator">&lt;&lt;</span> driver<span class="token punctuation">.</span><span class="token function">getErrorString</span><span class="token punctuation">(</span>configResult<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// Request with error handling</span>
RequestResult requestResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">requestData</span><span class="token punctuation">(</span><span class="token string">"A001"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>requestResult <span class="token operator">!=</span> RequestResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Request Error: "</span> <span class="token operator">&lt;&lt;</span> driver<span class="token punctuation">.</span><span class="token function">getErrorString</span><span class="token punctuation">(</span>requestResult<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// Response handling with timeout and error checking - Use address from S001</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
ResponseResult responseResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>targetSlaveAddress<span class="token punctuation">,</span> responseData<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>responseResult <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>TIMEOUT_ERROR<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Response Timeout: "</span> <span class="token operator">&lt;&lt;</span> driver<span class="token punctuation">.</span><span class="token function">getErrorString</span><span class="token punctuation">(</span>responseResult<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
<span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>responseResult <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>CRC_ERROR<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"CRC Error: "</span> <span class="token operator">&lt;&lt;</span> driver<span class="token punctuation">.</span><span class="token function">getErrorString</span><span class="token punctuation">(</span>responseResult<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    <span class="token comment">// Automatic retry mechanism will be triggered</span>
<span class="token punctuation">}</span>
</code></pre><h2 id="5-response-data-processing">5. Response Data Processing </h2>
<h3 id="51-slave-response-api">5.1 Slave Response API </h3>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Slave Response API - Handles both acknowledgments and data</span>
ResponseResult <span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span> slaveAddress<span class="token punctuation">,</span> <span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
                                   <span class="token keyword keyword-uint32_t">uint32_t</span> timeoutMs <span class="token operator">=</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Advanced response handling with wait options</span>
ResponseResult <span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span><span class="token keyword keyword-uint8_t">uint8_t</span> slaveAddress<span class="token punctuation">,</span> <span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
                                   <span class="token keyword keyword-bool">bool</span> waitForData<span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span> timeoutMs<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h3 id="52-data-extraction-helper-functions">5.2 Data Extraction Helper Functions </h3>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// PayloadDataExtractor class for consistent data handling</span>
<span class="token keyword keyword-class">class</span> <span class="token class-name">PayloadDataExtractor</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token comment">// Extract key from bytes 0-3 (4-byte ASCII string)</span>
    <span class="token keyword keyword-static">static</span> std<span class="token double-colon punctuation">::</span>string <span class="token function">extractKey</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Extract 32-bit integer from bytes 4-7 (little-endian)</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-uint32_t">uint32_t</span> <span class="token function">extractInteger</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Extract IEEE 754 single-precision float from bytes 4-7</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-float">float</span> <span class="token function">extractFloat</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Extract IEEE 754 double-precision float from bytes 4-11</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-double">double</span> <span class="token function">extractDouble</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// Extract dual 32-bit integers from bytes 4-7 and 8-11</span>
    <span class="token keyword keyword-static">static</span> std<span class="token double-colon punctuation">::</span>pair<span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&gt;</span> <span class="token function">extractDualIntegers</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Convenience functions (same as PayloadDataExtractor methods)</span>
std<span class="token double-colon punctuation">::</span>string <span class="token function">extractKey</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-uint32_t">uint32_t</span> <span class="token function">extractInteger</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-float">float</span> <span class="token function">extractFloat</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-double">double</span> <span class="token function">extractDouble</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
std<span class="token double-colon punctuation">::</span>pair<span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&gt;</span> <span class="token function">extractDualIntegers</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// Example usage - Use address from S001:</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
ResponseResult result <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>targetSlaveAddress<span class="token punctuation">,</span> responseData<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>result <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>string key <span class="token operator">=</span> <span class="token function">extractKey</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>      <span class="token comment">// Bytes 0-3</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> value <span class="token operator">=</span> <span class="token function">extractInteger</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>   <span class="token comment">// Bytes 4-7</span>

    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>key <span class="token operator">==</span> <span class="token string">"U001"</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"SEL threshold confirmed: "</span> <span class="token operator">&lt;&lt;</span> value <span class="token operator">&lt;&lt;</span> <span class="token string">" mA"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>key <span class="token operator">==</span> <span class="token string">"A002"</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Device status: 0x"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>hex <span class="token operator">&lt;&lt;</span> value <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><h2 id="6-complete-usage-workflow-examples">6. Complete Usage Workflow Examples </h2>
<h3 id="61-phase-1-system-setup-s-series-implementation">6.1 Phase 1: System Setup (S-series Implementation) </h3>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Step 1: Initialize driver and check hardware</span>
RS485Driver driver<span class="token punctuation">;</span>
ConnectionResult portResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">openPort</span><span class="token punctuation">(</span><span class="token string">"COM3"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>portResult <span class="token operator">!=</span> ConnectionResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Failed to open port: "</span> <span class="token operator">&lt;&lt;</span> driver<span class="token punctuation">.</span><span class="token function">getErrorString</span><span class="token punctuation">(</span>portResult<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    <span class="token keyword keyword-return">return</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// Step 2: Detect devices (ensure only one slave for address assignment)</span>
std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span><span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">&gt;</span> detectedAddresses<span class="token punctuation">;</span>
DetectionResult detection <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">detectMultipleDevices</span><span class="token punctuation">(</span>detectedAddresses<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>detectedAddresses<span class="token punctuation">.</span><span class="token function">size</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&gt;</span> <span class="token number">1</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Multiple slaves detected. Please connect only one slave for address assignment."</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    <span class="token keyword keyword-return">return</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// Step 3: Set slave address (broadcast to default address 0x00)</span>
ConfigurationResult s001Result <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">configureSystemSettings</span><span class="token punctuation">(</span><span class="token string">"S001"</span><span class="token punctuation">,</span> <span class="token number">5</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>s001Result <span class="token operator">!=</span> ConfigurationResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"S001 Error: "</span> <span class="token operator">&lt;&lt;</span> driver<span class="token punctuation">.</span><span class="token function">getErrorString</span><span class="token punctuation">(</span>s001Result<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// Step 4: Receive S001 acknowledgment - Use the address we just set</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> s001Response<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> assignedAddress <span class="token operator">=</span> <span class="token number">5</span><span class="token punctuation">;</span>  <span class="token comment">// The address we assigned in Step 3</span>
ResponseResult s001Ack <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>assignedAddress<span class="token punctuation">,</span> s001Response<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>s001Ack <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>string key <span class="token operator">=</span> <span class="token function">extractKey</span><span class="token punctuation">(</span>s001Response<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-uint32_t">uint32_t</span> confirmedAddress <span class="token operator">=</span> <span class="token function">extractInteger</span><span class="token punctuation">(</span>s001Response<span class="token punctuation">)</span><span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Address assignment confirmed: "</span> <span class="token operator">&lt;&lt;</span> confirmedAddress <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// Step 5: Set baud rate</span>
ConfigurationResult s002Result <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">configureSystemSettings</span><span class="token punctuation">(</span><span class="token string">"S002"</span><span class="token punctuation">,</span> <span class="token number">115200</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> s002Response<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>assignedAddress<span class="token punctuation">,</span> s002Response<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h3 id="62-phase-2-user-configuration-u-series-implementation">6.2 Phase 2: User Configuration (U-series Implementation) </h3>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Configure SEL detection parameters with acknowledgment checking</span>
<span class="token keyword keyword-struct">struct</span> <span class="token class-name">ConfigCommand</span> <span class="token punctuation">{</span>
    std<span class="token double-colon punctuation">::</span>string key<span class="token punctuation">;</span>
    <span class="token keyword keyword-uint64_t">uint64_t</span> value<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>string description<span class="token punctuation">;</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span>ConfigCommand<span class="token operator">&gt;</span> userConfigs <span class="token operator">=</span> <span class="token punctuation">{</span>
    <span class="token punctuation">{</span><span class="token string">"U001"</span><span class="token punctuation">,</span> <span class="token number">250</span><span class="token punctuation">,</span> <span class="token string">"SEL detection threshold: 250mA"</span><span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token punctuation">{</span><span class="token string">"U002"</span><span class="token punctuation">,</span> <span class="token number">1500</span><span class="token punctuation">,</span> <span class="token string">"SEL max amplitude: 1500mA"</span><span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token punctuation">{</span><span class="token string">"U003"</span><span class="token punctuation">,</span> <span class="token number">3</span><span class="token punctuation">,</span> <span class="token string">"Detection count before power cycle: 3"</span><span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token punctuation">{</span><span class="token string">"U004"</span><span class="token punctuation">,</span> <span class="token number">600</span><span class="token punctuation">,</span> <span class="token string">"Power cycle duration: 600ms"</span><span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token punctuation">{</span><span class="token string">"U005"</span><span class="token punctuation">,</span> <span class="token number">0x100000000ULL</span><span class="token punctuation">,</span> <span class="token string">"Enable GPIO input channel 0"</span><span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token punctuation">{</span><span class="token string">"U006"</span><span class="token punctuation">,</span> <span class="token number">0x100000001ULL</span><span class="token punctuation">,</span> <span class="token string">"Enable GPIO output channel 1"</span><span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-auto">auto</span><span class="token operator">&amp;</span> config <span class="token operator">:</span> userConfigs<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// Send configuration command</span>
    ConfigurationResult result <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span>config<span class="token punctuation">.</span>key<span class="token punctuation">,</span> config<span class="token punctuation">.</span>value<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>result <span class="token operator">!=</span> ConfigurationResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Config Error for "</span> <span class="token operator">&lt;&lt;</span> config<span class="token punctuation">.</span>key <span class="token operator">&lt;&lt;</span> <span class="token string">": "</span>
                  <span class="token operator">&lt;&lt;</span> driver<span class="token punctuation">.</span><span class="token function">getErrorString</span><span class="token punctuation">(</span>result<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
        <span class="token keyword keyword-continue">continue</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Receive acknowledgment - Use address from S001</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> ackData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
    ResponseResult ackResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>assignedAddress<span class="token punctuation">,</span> ackData<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>ackResult <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>string ackKey <span class="token operator">=</span> <span class="token function">extractKey</span><span class="token punctuation">(</span>ackData<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-uint32_t">uint32_t</span> ackValue <span class="token operator">=</span> <span class="token function">extractInteger</span><span class="token punctuation">(</span>ackData<span class="token punctuation">)</span><span class="token punctuation">;</span>
        std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> config<span class="token punctuation">.</span>description <span class="token operator">&lt;&lt;</span> <span class="token string">" - Confirmed: "</span> <span class="token operator">&lt;&lt;</span> ackValue <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"ACK Error for "</span> <span class="token operator">&lt;&lt;</span> config<span class="token punctuation">.</span>key <span class="token operator">&lt;&lt;</span> <span class="token string">": "</span>
                  <span class="token operator">&lt;&lt;</span> driver<span class="token punctuation">.</span><span class="token function">getErrorString</span><span class="token punctuation">(</span>ackResult<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><h3 id="63-phase-3-data-monitoring-a-series-implementation">6.3 Phase 3: Data Monitoring (A-series Implementation) </h3>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Request various types of data</span>
std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span>std<span class="token double-colon punctuation">::</span>string<span class="token operator">&gt;</span> dataRequests <span class="token operator">=</span> <span class="token punctuation">{</span><span class="token string">"A001"</span><span class="token punctuation">,</span> <span class="token string">"A002"</span><span class="token punctuation">,</span> <span class="token string">"A003"</span><span class="token punctuation">,</span> <span class="token string">"A004"</span><span class="token punctuation">,</span> <span class="token string">"A005"</span><span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-auto">auto</span><span class="token operator">&amp;</span> request <span class="token operator">:</span> dataRequests<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// Send data request (non-blocking)</span>
    RequestResult reqResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">requestData</span><span class="token punctuation">(</span>request<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>reqResult <span class="token operator">!=</span> RequestResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Request Error for "</span> <span class="token operator">&lt;&lt;</span> request <span class="token operator">&lt;&lt;</span> <span class="token string">": "</span>
                  <span class="token operator">&lt;&lt;</span> driver<span class="token punctuation">.</span><span class="token function">getErrorString</span><span class="token punctuation">(</span>reqResult<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
        <span class="token keyword keyword-continue">continue</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// Receive response data - Use address from S001</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> responseData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
    ResponseResult respResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>assignedAddress<span class="token punctuation">,</span> responseData<span class="token punctuation">,</span> <span class="token number">2000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>respResult <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>string responseKey <span class="token operator">=</span> <span class="token function">extractKey</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>

        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>responseKey <span class="token operator">==</span> <span class="token string">"A001"</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token comment">// SEL event log - JSON data pointer</span>
            <span class="token keyword keyword-uint64_t">uint64_t</span> jsonPointer <span class="token operator">=</span> <span class="token function">extractDouble</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
            std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"SEL Event Log received, JSON pointer: 0x"</span>
                      <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>hex <span class="token operator">&lt;&lt;</span> jsonPointer <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
        <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>responseKey <span class="token operator">==</span> <span class="token string">"A002"</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token comment">// Device status - 16-bit flags</span>
            <span class="token keyword keyword-uint32_t">uint32_t</span> statusFlags <span class="token operator">=</span> <span class="token function">extractInteger</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
            std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Device Status: 0x"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>hex <span class="token operator">&lt;&lt;</span> statusFlags <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
        <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>responseKey <span class="token operator">==</span> <span class="token string">"A003"</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token comment">// Firmware version</span>
            <span class="token keyword keyword-uint32_t">uint32_t</span> version <span class="token operator">=</span> <span class="token function">extractInteger</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
            std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Firmware Version: "</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>dec <span class="token operator">&lt;&lt;</span> version <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
        <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>responseKey <span class="token operator">==</span> <span class="token string">"A004"</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token comment">// System statistics - JSON data pointer</span>
            <span class="token keyword keyword-uint64_t">uint64_t</span> statsPointer <span class="token operator">=</span> <span class="token function">extractDouble</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
            std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"System Statistics received, JSON pointer: 0x"</span>
                      <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>hex <span class="token operator">&lt;&lt;</span> statsPointer <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
        <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>responseKey <span class="token operator">==</span> <span class="token string">"A005"</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token comment">// Current configuration - JSON data pointer</span>
            <span class="token keyword keyword-uint64_t">uint64_t</span> configPointer <span class="token operator">=</span> <span class="token function">extractDouble</span><span class="token punctuation">(</span>responseData<span class="token punctuation">)</span><span class="token punctuation">;</span>
            std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Current Configuration received, JSON pointer: 0x"</span>
                      <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>hex <span class="token operator">&lt;&lt;</span> configPointer <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Response Error for "</span> <span class="token operator">&lt;&lt;</span> request <span class="token operator">&lt;&lt;</span> <span class="token string">": "</span>
                  <span class="token operator">&lt;&lt;</span> driver<span class="token punctuation">.</span><span class="token function">getErrorString</span><span class="token punctuation">(</span>respResult<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><h3 id="64-phase-4-model-data-operations-w-series-implementation">6.4 Phase 4: Model Data Operations (W-series Implementation) </h3>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Write AI model weights to FRAM</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> weightData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token punctuation">{</span>
    <span class="token char">'W'</span><span class="token punctuation">,</span> <span class="token char">'0'</span><span class="token punctuation">,</span> <span class="token char">'0'</span><span class="token punctuation">,</span> <span class="token char">'1'</span><span class="token punctuation">,</span>  <span class="token comment">// Key: "W001"</span>
    <span class="token number">0x00</span><span class="token punctuation">,</span> <span class="token number">0x00</span><span class="token punctuation">,</span> <span class="token number">0x80</span><span class="token punctuation">,</span> <span class="token number">0x3F</span><span class="token punctuation">,</span>  <span class="token comment">// Float value: 1.0f in IEEE 754</span>
    <span class="token number">0x00</span><span class="token punctuation">,</span> <span class="token number">0x00</span><span class="token punctuation">,</span> <span class="token number">0x00</span><span class="token punctuation">,</span> <span class="token number">0x00</span>   <span class="token comment">// Padding</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token comment">// Write operation</span>
ConfigurationResult writeResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">modelDataOperation</span><span class="token punctuation">(</span><span class="token number">0x1000</span><span class="token punctuation">,</span> weightData<span class="token punctuation">,</span> <span class="token boolean">true</span><span class="token punctuation">,</span> <span class="token number">12</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>writeResult <span class="token operator">==</span> ConfigurationResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> writeAck<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
    ResponseResult writeAckResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>assignedAddress<span class="token punctuation">,</span> writeAck<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>writeAckResult <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Model weight written successfully to FRAM address 0x1000"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// Read AI model bias from FRAM</span>
<span class="token keyword keyword-uint8_t">uint8_t</span> biasData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
RequestResult readResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">modelDataOperation</span><span class="token punctuation">(</span><span class="token number">0x2000</span><span class="token punctuation">,</span> biasData<span class="token punctuation">,</span> <span class="token boolean">false</span><span class="token punctuation">,</span> <span class="token number">12</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>readResult <span class="token operator">==</span> RequestResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-uint8_t">uint8_t</span> readResponse<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
    ResponseResult readRespResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>assignedAddress<span class="token punctuation">,</span> readResponse<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>readRespResult <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>string key <span class="token operator">=</span> <span class="token function">extractKey</span><span class="token punctuation">(</span>readResponse<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-float">float</span> biasValue <span class="token operator">=</span> <span class="token function">extractFloat</span><span class="token punctuation">(</span>readResponse<span class="token punctuation">)</span><span class="token punctuation">;</span>
        std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Model bias read from FRAM: "</span> <span class="token operator">&lt;&lt;</span> biasValue <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><h3 id="65-complete-error-handling-and-recovery">6.5 Complete Error Handling and Recovery </h3>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Comprehensive error handling with automatic retry</span>
<span class="token keyword keyword-void">void</span> <span class="token function">robustCommunication</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-int">int</span> MAX_RETRIES <span class="token operator">=</span> <span class="token number">3</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token keyword keyword-int">int</span> attempt <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> attempt <span class="token operator">&lt;</span> MAX_RETRIES<span class="token punctuation">;</span> attempt<span class="token operator">++</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// Check buffer status before transmission</span>
        BufferStatus bufferStatus<span class="token punctuation">;</span>
        BufferResult bufferResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">getBufferStatus</span><span class="token punctuation">(</span>bufferStatus<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>bufferResult <span class="token operator">!=</span> BufferResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Buffer check failed: "</span> <span class="token operator">&lt;&lt;</span> driver<span class="token punctuation">.</span><span class="token function">getErrorString</span><span class="token punctuation">(</span>bufferResult<span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
            driver<span class="token punctuation">.</span><span class="token function">clearBuffer</span><span class="token punctuation">(</span>BufferType<span class="token double-colon punctuation">::</span>BOTH<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword keyword-continue">continue</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>bufferStatus<span class="token punctuation">.</span>isUplinkFull<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Uplink buffer full, clearing..."</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
            driver<span class="token punctuation">.</span><span class="token function">clearBuffer</span><span class="token punctuation">(</span>BufferType<span class="token double-colon punctuation">::</span>UPLINK<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>

        <span class="token comment">// Attempt configuration</span>
        ConfigurationResult configResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U001"</span><span class="token punctuation">,</span> <span class="token number">250</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>configResult <span class="token operator">==</span> ConfigurationResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token comment">// Wait for acknowledgment - Use address from S001</span>
            <span class="token keyword keyword-uint8_t">uint8_t</span> ackData<span class="token punctuation">[</span><span class="token number">12</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
            ResponseResult ackResult <span class="token operator">=</span> driver<span class="token punctuation">.</span><span class="token function">receiveSlaveResponse</span><span class="token punctuation">(</span>assignedAddress<span class="token punctuation">,</span> ackData<span class="token punctuation">,</span> <span class="token number">1000</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

            <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>ackResult <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>SUCCESS<span class="token punctuation">)</span> <span class="token punctuation">{</span>
                std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Configuration successful on attempt "</span> <span class="token operator">&lt;&lt;</span> <span class="token punctuation">(</span>attempt <span class="token operator">+</span> <span class="token number">1</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
                <span class="token keyword keyword-return">return</span><span class="token punctuation">;</span>  <span class="token comment">// Success</span>
            <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>ackResult <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>CRC_ERROR<span class="token punctuation">)</span> <span class="token punctuation">{</span>
                std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"CRC error detected, retrying..."</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
                <span class="token keyword keyword-continue">continue</span><span class="token punctuation">;</span>  <span class="token comment">// Retry</span>
            <span class="token punctuation">}</span> <span class="token keyword keyword-else">else</span> <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>ackResult <span class="token operator">==</span> ResponseResult<span class="token double-colon punctuation">::</span>TIMEOUT<span class="token punctuation">)</span> <span class="token punctuation">{</span>
                std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Timeout occurred, retrying..."</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
                <span class="token keyword keyword-continue">continue</span><span class="token punctuation">;</span>  <span class="token comment">// Retry</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>

    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Communication failed after "</span> <span class="token operator">&lt;&lt;</span> MAX_RETRIES <span class="token operator">&lt;&lt;</span> <span class="token string">" attempts"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h2 id="7-key-design-benefits">7. Key Design Benefits </h2>
<h3 id="71-user-simplicity">7.1 User Simplicity </h3>
<ul>
<li><strong>Raw Data Input</strong>: Users provide values directly (250, 1500, 3.14159f)</li>
<li><strong>Automatic Conversion</strong>: Driver handles all binary encoding/decoding</li>
<li><strong>Cross-Platform</strong>: Same code works on Windows/Linux/ARM</li>
<li><strong>Enhanced Type Safety</strong>: Specific result types (ConfigurationResult, RequestResult, ResponseResult) replace generic RS485Error for better semantic clarity</li>
<li><strong>Universal Data Format</strong>: IEEE 754 and little-endian standards ensure compatibility</li>
<li><strong>Language Independence</strong>: Same format works in C++, Python, Java, C#</li>
<li><strong>Payload-Centric Design</strong>: All communication based on 12-byte payload structure (Key + Value)</li>
</ul>
<h3 id="72-protocol-abstraction">7.2 Protocol Abstraction </h3>
<ul>
<li><strong>Hidden Complexity</strong>: Users never see 16-byte frames or protocol details</li>
<li><strong>Automatic Routing</strong>: Function codes route to correct API categories</li>
<li><strong>Buffer Management</strong>: Automatic buffer checking before transmission</li>
<li><strong>Error Recovery</strong>: Intelligent retry mechanism for transient errors</li>
</ul>
<h3 id="73-enterprise-features">7.3 Enterprise Features </h3>
<ul>
<li><strong>FTDI Integration</strong>: Built-in USB-RS485 converter support with filter driver architecture</li>
<li><strong>Single Executable</strong>: No separate driver installation required</li>
<li><strong>Real-Time Performance</strong>: Fixed-size payload buffers (180 bytes total) for deterministic behavior</li>
<li><strong>Comprehensive Logging</strong>: Detailed error reporting and diagnostics with specific result types</li>
<li><strong>Windows Driver Kit</strong>: UMDF 2 filter driver framework with DeviceIoControl interface</li>
<li><strong>Thread-Safe Operations</strong>: All buffer operations protected by synchronization</li>
<li><strong>Non-Blocking Design</strong>: Driver operations never block user threads with dedicated thread pools</li>
<li><strong>Predictive Buffer Management</strong>: Overflow prevention with frame-by-frame transmission checking</li>
</ul>
<h2 id="8-data-format-specification">8. Data Format Specification </h2>
<h3 id="81-cross-platform-data-format">8.1 Cross-Platform Data Format </h3>
<p><strong>Universal 12-byte Payload Structure:</strong></p>
<ul>
<li><strong>Bytes 0-3</strong>: Command Key (4-byte ASCII string, e.g., "S001", "U001", "A001")</li>
<li><strong>Bytes 4-11</strong>: Value Data (8-byte binary in little-endian format)</li>
</ul>
<p><strong>Data Type Encoding:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// Integer values (32-bit, uses bytes 4-7, bytes 8-11 set to zero)</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U001"</span><span class="token punctuation">,</span> <span class="token number">250</span><span class="token punctuation">)</span><span class="token punctuation">;</span>        <span class="token comment">// 250 mA threshold</span>

<span class="token comment">// Floating-point values (IEEE 754 standard)</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"W001"</span><span class="token punctuation">,</span> <span class="token number">3.14159f</span><span class="token punctuation">)</span><span class="token punctuation">;</span>   <span class="token comment">// Single precision (bytes 4-7)</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"W002"</span><span class="token punctuation">,</span> <span class="token number">2.71828</span><span class="token punctuation">)</span><span class="token punctuation">;</span>    <span class="token comment">// Double precision (bytes 4-11)</span>

<span class="token comment">// Dual integer values (GPIO configuration)</span>
driver<span class="token punctuation">.</span><span class="token function">configureUserSettings</span><span class="token punctuation">(</span><span class="token string">"U005"</span><span class="token punctuation">,</span> <span class="token number">0x100000000ULL</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// Channel=0, Enable=1</span>
</code></pre><p><strong>Cross-Platform Guarantees:</strong></p>
<ul>
<li><strong>Byte Order</strong>: All multi-byte values use little-endian format</li>
<li><strong>Alignment</strong>: No special alignment requirements</li>
<li><strong>Padding</strong>: Unused bytes set to zero</li>
<li><strong>IEEE 754</strong>: Standard floating-point representation</li>
<li><strong>Type Safety</strong>: API functions handle conversion automatically</li>
</ul>
<h2 id="9-implementation-notes">9. Implementation Notes </h2>
<ul>
<li><strong>Address Management</strong>: S001 sets target address for subsequent U/A-series commands
<ul>
<li><strong>Best Practice</strong>: Store the slave address in a variable after S001 command</li>
<li><strong>Usage Pattern</strong>: Use the same address variable for all subsequent receiveSlaveResponse() calls</li>
<li><strong>Multi-Slave Support</strong>: Change address using S001 before communicating with different slaves</li>
</ul>
</li>
<li><strong>Configuration Persistence</strong>: All U-series settings saved to FRAM automatically</li>
<li><strong>Buffer Overflow</strong>: Configurable policies (discard oldest/newest, trigger error)</li>
<li><strong>Multi-Frame Responses</strong>: Automatic handling for responses &gt;8 bytes</li>
<li><strong>Acknowledgment System</strong>: Mandatory acknowledgments for reliability</li>
</ul>
<h2 id="10-api-summary-table">10. API Summary Table </h2>
<table>
<thead>
<tr>
<th>API Category</th>
<th>Function</th>
<th>Purpose</th>
<th>Command Series</th>
<th>Example</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Error Handle API</strong></td>
<td><code>getErrorString(error)</code></td>
<td>Detailed error information</td>
<td>N/A</td>
<td><code>driver.getErrorString(result)</code></td>
</tr>
<tr>
<td><strong>Master Broadcasting API</strong></td>
<td><code>configureSystemSettings(key, value)</code></td>
<td>System configuration</td>
<td>S-series</td>
<td><code>configureSystemSettings("S001", 5)</code></td>
</tr>
<tr>
<td><strong>Master Assign Data API</strong></td>
<td><code>configureUserSettings(key, value)</code></td>
<td>User configuration</td>
<td>U-series</td>
<td><code>configureUserSettings("U001", 250)</code></td>
</tr>
<tr>
<td><strong>Master Assign Data API</strong></td>
<td><code>modelDataOperation(addr, data, write, len)</code></td>
<td>AI model data</td>
<td>W-series</td>
<td><code>modelDataOperation(0x1000, data, true, 12)</code></td>
</tr>
<tr>
<td><strong>Master Request API</strong></td>
<td><code>requestData(key)</code></td>
<td>Data queries</td>
<td>A-series</td>
<td><code>requestData("A001")</code></td>
</tr>
<tr>
<td><strong>Slave Response API</strong></td>
<td><code>receiveSlaveResponse(addr, data, timeout)</code></td>
<td>Receive responses</td>
<td>N/A</td>
<td><code>receiveSlaveResponse(targetSlaveAddress, data, 1000)</code></td>
</tr>
</tbody>
</table>
<p><strong>Key Design Principles:</strong></p>
<ul>
<li><strong>12-byte payload focus</strong>: All communication based on Key(4) + Value(8) structure with payload-centric buffer management</li>
<li><strong>Address consistency</strong>: Use S001-assigned address for all subsequent operations</li>
<li><strong>Enhanced type safety</strong>: Specific result types (ConfigurationResult, RequestResult, ResponseResult) replace generic RS485Error for better semantic clarity</li>
<li><strong>Cross-platform compatibility</strong>: Universal data format works on Windows/Linux/ARM with IEEE 754 and little-endian standards</li>
<li><strong>Enterprise-ready</strong>: Single executable with integrated FTDI filter driver support and UMDF 2 framework</li>
<li><strong>Non-blocking architecture</strong>: Dedicated thread pools ensure user threads never block during RS485 operations</li>
<li><strong>Predictive buffer management</strong>: Frame-by-frame overflow prevention with intelligent scheduling</li>
</ul>

      </div>
      
      
    
    
    
    
    
    
  
    </body></html>