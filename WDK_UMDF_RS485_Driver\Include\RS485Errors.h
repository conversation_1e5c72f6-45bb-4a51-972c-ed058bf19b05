#pragma once

//
// RS485 Driver Error Codes and Result Types
// Comprehensive error handling for UMDF driver implementation
//

#include "RS485Common.h"

//
// Main RS485 Error Enumeration
//
typedef enum _RS485_ERROR {
    // Success code
    RS485_SUCCESS = 0,                      // Operation completed successfully

    // FTDI Driver related errors (100-199) - Inherited from FTDI VCP Driver
    // Transient errors - may succeed on retry
    RS485_CONNECTION_ERROR = 100,           // Failed to open/connect to the serial port
    RS485_DEVICE_BUSY = 102,                // Devi<PERSON> is busy processing another command
    RS485_PORT_NOT_AVAILABLE = 103,         // COM port not available or in use
    RS485_DRIVER_NOT_LOADED = 104,          // FTDI VCP driver not properly loaded
    RS485_INSUFFICIENT_RESOURCES = 105,     // System resources insufficient

    // Permanent errors - require user intervention
    RS485_DEVICE_NOT_FOUND = 101,           // Specified device not found
    RS485_INVALID_HANDLE = 106,             // Invalid device handle
    RS485_INVALID_BAUD_RATE = 107,          // Unsupported baud rate specified
    RS485_INVALID_PARAMETER = 108,          // Invalid parameter provided

    // Buffer Management errors (150-199) - Critical for RS485 operation
    // Transient errors - may succeed on retry
    RS485_INSUFFICIENT_BUFFER = 150,        // Buffer too small for operation
    RS485_BUFFER_OVERFLOW = 151,            // Buffer overflow detected
    RS485_BUFFER_UNDERFLOW = 152,           // Attempting to read from empty buffer

    // Permanent errors - require user intervention
    RS485_BUFFER_ALLOCATION_FAILED = 153,   // Failed to allocate driver-managed buffers
    RS485_INVALID_BUFFER_SIZE = 154,        // Invalid buffer size specified

    // ZES Protocol related errors (200-299)
    // Transient errors - may succeed on retry
    RS485_PROTOCOL_ERROR = 200,             // Protocol violation detected
    RS485_CRC_ERROR = 201,                  // CRC check failed
    RS485_TIMEOUT_ERROR = 202,              // Command timed out waiting for response
    RS485_FRAME_SYNC_ERROR = 203,           // Frame synchronization lost
    RS485_RETRY_LIMIT_EXCEEDED = 204,       // Maximum retry attempts exceeded

    // Permanent errors - require user intervention
    RS485_UNSUPPORTED_OPERATION = 205,      // Operation not supported by device
    RS485_INVALID_COMMAND_KEY = 206,        // Invalid command key provided
    RS485_INVALID_SLAVE_ADDRESS = 207,      // Invalid slave address (must be 1-31)
    RS485_BROADCAST_CONFLICT = 208,         // Multiple slaves detected during broadcast

    // Memory operation errors (300-399)
    // Transient errors - may succeed on retry
    RS485_MEMORY_ACCESS_ERROR = 300,        // Error accessing FRAM memory
    RS485_MEMORY_WRITE_FAILED = 301,        // Failed to write to FRAM memory
    RS485_MEMORY_READ_FAILED = 302,         // Failed to read from FRAM memory

    // Permanent errors - require user intervention
    RS485_INVALID_MEMORY_ADDRESS = 303,     // Invalid memory address specified
    RS485_MEMORY_RANGE_ERROR = 304,         // Memory address out of valid range
    RS485_MEMORY_PROTECTION_ERROR = 305,    // Attempting to write to protected memory

    // Data handling errors (400-499)
    // Permanent errors - require user intervention
    RS485_DATA_FORMAT_ERROR = 400,          // Data format does not match expected format
    RS485_PAYLOAD_SIZE_ERROR = 401,         // Payload size exceeds 12-byte limit
    RS485_JSON_PARSE_ERROR = 402,           // Failed to parse JSON response data
    RS485_CHECKSUM_MISMATCH = 403,          // Data checksum verification failed

    // Function Code Processing errors (500-599)
    // Permanent errors - require user intervention
    RS485_INVALID_FUNCTION_CODE = 500,      // Invalid function code in ID byte
    RS485_FUNCTION_CODE_MISMATCH = 501,     // Function code doesn't match expected API
    RS485_RESPONSE_TYPE_ERROR = 502,        // Unexpected response type received
    RS485_API_CATEGORY_ERROR = 503          // API call doesn't match function code requirements
} RS485_ERROR;

//
// Specific Result Types for Different API Categories
//

// Connection and port management results
typedef enum _RS485_CONNECTION_RESULT {
    ConnectionResultSuccess = 0,
    ConnectionResultPortNotFound,
    ConnectionResultPortAlreadyOpen,
    ConnectionResultPortAccessDenied,
    ConnectionResultInvalidPortName,
    ConnectionResultConnectionFailed,
    ConnectionResultTimeout
} RS485_CONNECTION_RESULT;

// Device enumeration and detection results
typedef enum _RS485_ENUMERATION_RESULT {
    EnumerationResultSuccess = 0,
    EnumerationResultNoDevicesFound,
    EnumerationResultSystemError,
    EnumerationResultInsufficientBuffer,
    EnumerationResultAccessDenied
} RS485_ENUMERATION_RESULT;

typedef enum _RS485_DETECTION_RESULT {
    DetectionResultSuccess = 0,
    DetectionResultMultipleDevicesDetected,
    DetectionResultNoResponse,
    DetectionResultCommunicationError,
    DetectionResultTimeout
} RS485_DETECTION_RESULT;

// Buffer management results
typedef enum _RS485_BUFFER_RESULT {
    BufferResultSuccess = 0,
    BufferResultInsufficientBuffer,
    BufferResultBufferOverflow,
    BufferResultBufferUnderflow,
    BufferResultInvalidBufferType,
    BufferResultBufferNotInitialized
} RS485_BUFFER_RESULT;

// Hardware and performance monitoring results
typedef enum _RS485_HARDWARE_RESULT {
    HardwareResultSuccess = 0,
    HardwareResultHardwareNotReady,
    HardwareResultHardwareError,
    HardwareResultSensorFailure,
    HardwareResultCommunicationLost
} RS485_HARDWARE_RESULT;

typedef enum _RS485_PERFORMANCE_RESULT {
    PerformanceResultSuccess = 0,
    PerformanceResultMetricsNotAvailable,
    PerformanceResultInsufficientData,
    PerformanceResultCalculationError
} RS485_PERFORMANCE_RESULT;

typedef enum _RS485_LINE_RESULT {
    LineResultSuccess = 0,
    LineResultLineNotReady,
    LineResultSignalError,
    LineResultBusFault,
    LineResultElectricalFault
} RS485_LINE_RESULT;

// Configuration and data operation results
typedef enum _RS485_CONFIGURATION_RESULT {
    ConfigurationResultSuccess = 0,
    ConfigurationResultInvalidCommand,
    ConfigurationResultInvalidValue,
    ConfigurationResultDeviceNotResponding,
    ConfigurationResultConfigurationFailed,
    ConfigurationResultVerificationFailed,
    ConfigurationResultBufferFull,
    ConfigurationResultTimeout
} RS485_CONFIGURATION_RESULT;

typedef enum _RS485_VERIFICATION_RESULT {
    VerificationResultSuccess = 0,
    VerificationResultMismatchDetected,
    VerificationResultVerificationFailed,
    VerificationResultDeviceNotResponding,
    VerificationResultTimeout
} RS485_VERIFICATION_RESULT;

typedef enum _RS485_MODEL_DATA_RESULT {
    ModelDataResultSuccess = 0,
    ModelDataResultInvalidAddress,
    ModelDataResultInvalidDataLength,
    ModelDataResultWriteFailed,
    ModelDataResultReadFailed,
    ModelDataResultMemoryError,
    ModelDataResultTimeout
} RS485_MODEL_DATA_RESULT;

// Request and response results
typedef enum _RS485_REQUEST_RESULT {
    RequestResultSuccess = 0,
    RequestResultInvalidRequest,
    RequestResultDeviceNotResponding,
    RequestResultRequestTimeout,
    RequestResultBufferFull
} RS485_REQUEST_RESULT;

typedef enum _RS485_RESPONSE_RESULT {
    ResponseResultSuccess = 0,
    ResponseResultNoDataAvailable,
    ResponseResultInvalidResponse,
    ResponseResultResponseTimeout,
    ResponseResultBufferEmpty,
    ResponseResultBufferFull,
    ResponseResultCrcError
} RS485_RESPONSE_RESULT;

// Port information results
typedef enum _RS485_PORT_RESULT {
    PortResultSuccess = 0,
    PortResultPortNotOpen,
    PortResultInvalidPort,
    PortResultInfoNotAvailable
} RS485_PORT_RESULT;

// Configuration query results
typedef enum _RS485_CONFIG_RESULT {
    ConfigResultSuccess = 0,
    ConfigResultConfigNotAvailable,
    ConfigResultInvalidConfig,
    ConfigResultReadError
} RS485_CONFIG_RESULT;

// IOCTL operation results
typedef enum _RS485_IOCTL_RESULT {
    IOCTLResultSuccess = 0,
    IOCTLResultInvalidIOCTLCode,
    IOCTLResultBufferTooSmall,
    IOCTLResultDeviceError,
    IOCTLResultOperationFailed
} RS485_IOCTL_RESULT;

//
// Error Categorization Functions
//
BOOLEAN RS485IsTransientError(_In_ RS485_ERROR Error);
BOOLEAN RS485IsPermanentError(_In_ RS485_ERROR Error);
BOOLEAN RS485ShouldRetry(_In_ RS485_ERROR Error, _In_ UINT32 RetryCount);

//
// Error String Functions
//
PCSTR RS485GetErrorString(_In_ RS485_ERROR Error);
PCSTR RS485GetConnectionResultString(_In_ RS485_CONNECTION_RESULT Result);
PCSTR RS485GetBufferResultString(_In_ RS485_BUFFER_RESULT Result);
PCSTR RS485GetConfigurationResultString(_In_ RS485_CONFIGURATION_RESULT Result);
PCSTR RS485GetRequestResultString(_In_ RS485_REQUEST_RESULT Result);
PCSTR RS485GetResponseResultString(_In_ RS485_RESPONSE_RESULT Result);

//
// Error Conversion Functions
//
RS485_ERROR RS485ConvertNTStatusToError(_In_ NTSTATUS Status);
RS485_CONNECTION_RESULT RS485ConvertErrorToConnectionResult(_In_ RS485_ERROR Error);
RS485_BUFFER_RESULT RS485ConvertErrorToBufferResult(_In_ RS485_ERROR Error);
RS485_CONFIGURATION_RESULT RS485ConvertErrorToConfigurationResult(_In_ RS485_ERROR Error);

//
// Error Logging and Reporting
//
VOID RS485LogError(_In_ RS485_ERROR Error, _In_opt_ PCSTR Context, _In_opt_ PCSTR Details);
VOID RS485LogWarning(_In_ RS485_ERROR Error, _In_opt_ PCSTR Context, _In_opt_ PCSTR Details);
VOID RS485LogInfo(_In_opt_ PCSTR Message, _In_opt_ PCSTR Details);

//
// Error Statistics
//
typedef struct _RS485_ERROR_STATISTICS {
    UINT32 TotalErrors;
    UINT32 TransientErrors;
    UINT32 PermanentErrors;
    UINT32 CrcErrors;
    UINT32 TimeoutErrors;
    UINT32 BufferOverflows;
    UINT32 ProtocolErrors;
    UINT32 HardwareErrors;
    UINT32 RetryAttempts;
    UINT32 SuccessfulRetries;
} RS485_ERROR_STATISTICS, *PRS485_ERROR_STATISTICS;

VOID RS485InitializeErrorStatistics(_Out_ PRS485_ERROR_STATISTICS Statistics);
VOID RS485UpdateErrorStatistics(_Inout_ PRS485_ERROR_STATISTICS Statistics, _In_ RS485_ERROR Error);
VOID RS485GetErrorStatistics(_In_ const RS485_ERROR_STATISTICS* Statistics, 
                            _Out_ PRS485_ERROR_STATISTICS OutputStatistics);

#endif // RS485_ERRORS_H
