//
// RS485 Device Implementation
// Additional device-specific functions and utilities
//

#include "RS485FilterDriver.h"

//
// Send Frame to Lower Driver
//
NTSTATUS RS485SendFrameToLowerDriver(
    _In_ PRS485_DEVICE_CONTEXT DeviceContext,
    _In_ const RS485_FRAME* Frame
)
{
    NTSTATUS status;
    WDFREQUEST request;
    WDF_OBJECT_ATTRIBUTES attributes;
    WDFMEMORY memory;

    if (DeviceContext == NULL || Frame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // Create request object
    WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
    status = WdfRequestCreate(&attributes, DeviceContext->LowerDeviceTarget, &request);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to create request: 0x%x", status);
        return status;
    }

    // Create memory object for frame data
    status = WdfMemoryCreate(&attributes, NonPagedPool, RS485_POOL_TAG_FRAME, 
                            sizeof(RS485_FRAME), &memory, NULL);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to create memory: 0x%x", status);
        WdfObjectDelete(request);
        return status;
    }

    // Copy frame data to memory
    PVOID buffer = WdfMemoryGetBuffer(memory, NULL);
    RtlCopyMemory(buffer, Frame, sizeof(RS485_FRAME));

    // Format request for write operation
    status = WdfIoTargetFormatRequestForWrite(DeviceContext->LowerDeviceTarget,
                                             request,
                                             memory,
                                             NULL,
                                             NULL);
    if (!NT_SUCCESS(status)) {
        RS485_ERROR_PRINT("Failed to format request: 0x%x", status);
        WdfObjectDelete(request);
        return status;
    }

    // Send request synchronously
    WDF_REQUEST_SEND_OPTIONS sendOptions;
    WDF_REQUEST_SEND_OPTIONS_INIT(&sendOptions, WDF_REQUEST_SEND_OPTION_SYNCHRONOUS);
    sendOptions.Timeout = WDF_REL_TIMEOUT_IN_MS(DeviceContext->ResponseTimeoutMs);

    BOOLEAN result = WdfRequestSend(request, DeviceContext->LowerDeviceTarget, &sendOptions);
    if (!result) {
        status = WdfRequestGetStatus(request);
        RS485_ERROR_PRINT("Failed to send request: 0x%x", status);
    } else {
        status = STATUS_SUCCESS;
        RS485_DEBUG_PRINT("Frame sent successfully to lower driver");
    }

    // Cleanup
    WdfObjectDelete(request);
    return status;
}

//
// Update Performance Metrics
//
VOID RS485UpdatePerformanceMetrics(
    _In_ PRS485_DEVICE_CONTEXT DeviceContext,
    _In_ BOOLEAN IsSuccess,
    _In_ UINT32 ResponseTimeMs
)
{
    if (DeviceContext == NULL) {
        return;
    }

    // Update frame counters
    if (IsSuccess) {
        DeviceContext->PerformanceMetrics.SuccessfulFrames++;
        DeviceContext->HardwareStatus.FramesSent++;
    } else {
        DeviceContext->PerformanceMetrics.FailedFrames++;
    }

    // Update response time metrics
    if (ResponseTimeMs > 0) {
        if (ResponseTimeMs > DeviceContext->PerformanceMetrics.MaxResponseTimeMs) {
            DeviceContext->PerformanceMetrics.MaxResponseTimeMs = ResponseTimeMs;
        }

        // Simple moving average for latency
        UINT32 totalFrames = DeviceContext->PerformanceMetrics.SuccessfulFrames + 
                            DeviceContext->PerformanceMetrics.FailedFrames;
        if (totalFrames > 0) {
            DeviceContext->PerformanceMetrics.AvgLatencyMs = 
                (DeviceContext->PerformanceMetrics.AvgLatencyMs * (totalFrames - 1) + ResponseTimeMs) / totalFrames;
        }
    }

    // Update success rate
    UINT32 totalFrames = DeviceContext->PerformanceMetrics.SuccessfulFrames + 
                        DeviceContext->PerformanceMetrics.FailedFrames;
    if (totalFrames > 0) {
        DeviceContext->PerformanceMetrics.FrameSuccessRate = 
            (DeviceContext->PerformanceMetrics.SuccessfulFrames * 100.0) / totalFrames;
    }

    RS485_DEBUG_PRINT("Performance updated: Success=%s, ResponseTime=%ums, SuccessRate=%.1f%%",
                      IsSuccess ? "Yes" : "No", ResponseTimeMs, 
                      DeviceContext->PerformanceMetrics.FrameSuccessRate);
}

//
// Schedule Resend Request
//
NTSTATUS RS485ScheduleResendRequest(
    _In_ PRS485_DEVICE_CONTEXT DeviceContext,
    _In_ const RS485_FRAME* OriginalFrame
)
{
    NTSTATUS status;
    RS485_FRAME resendFrame;

    if (DeviceContext == NULL || OriginalFrame == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // Build resend frame with function code 0b000
    RtlCopyMemory(&resendFrame, OriginalFrame, sizeof(RS485_FRAME));
    
    // Extract original device address and set resend function code
    UINT8 deviceAddress = RS485_EXTRACT_DEVICE_ADDRESS(OriginalFrame->IdByte);
    resendFrame.IdByte = RS485_BUILD_ID_BYTE(RS485_FUNCTION_RESEND_REQUEST, deviceAddress);
    
    // Recalculate CRC
    resendFrame.Crc8 = RS485CalculateCRC8(&resendFrame.IdByte, 13);

    // Send resend request
    status = RS485SendFrameToLowerDriver(DeviceContext, &resendFrame);
    if (NT_SUCCESS(status)) {
        RS485_DEBUG_PRINT("Resend request scheduled for device address %u", deviceAddress);
    } else {
        RS485_ERROR_PRINT("Failed to schedule resend request: 0x%x", status);
    }

    return status;
}

//
// Log Frame Error
//
VOID RS485LogFrameError(
    _In_ PRS485_DEVICE_CONTEXT DeviceContext,
    _In_ const RS485_FRAME* Frame,
    _In_ PCSTR Reason
)
{
    if (DeviceContext == NULL || Frame == NULL || Reason == NULL) {
        return;
    }

    UINT8 functionCode = RS485_EXTRACT_FUNCTION_CODE(Frame->IdByte);
    UINT8 deviceAddress = RS485_EXTRACT_DEVICE_ADDRESS(Frame->IdByte);

    RS485_ERROR_PRINT("Frame error: %s - Func=0x%X, Addr=%u, CRC=0x%02X",
                      Reason, functionCode, deviceAddress, Frame->Crc8);

    // Update error statistics
    RS485UpdateErrorStatistics(&DeviceContext->ErrorStatistics, RS485_PROTOCOL_ERROR);

    // Update hardware status
    DeviceContext->HardwareStatus.CrcErrors++;
}

//
// Handle Frame Error
//
VOID RS485HandleFrameError(
    _In_ PRS485_DEVICE_CONTEXT DeviceContext,
    _In_ const RS485_FRAME* Frame,
    _In_ RS485_ERROR Error
)
{
    if (DeviceContext == NULL) {
        return;
    }

    // Log the error
    if (Frame != NULL) {
        RS485LogFrameError(DeviceContext, Frame, RS485GetErrorString(Error));
    }

    // Update error statistics
    RS485UpdateErrorStatistics(&DeviceContext->ErrorStatistics, Error);

    // Update performance metrics
    RS485UpdatePerformanceMetrics(DeviceContext, FALSE, 0);

    // Handle specific error types
    switch (Error) {
        case RS485_CRC_ERROR:
            DeviceContext->HardwareStatus.CrcErrors++;
            if (Frame != NULL) {
                RS485ScheduleResendRequest(DeviceContext, Frame);
            }
            break;

        case RS485_TIMEOUT_ERROR:
            DeviceContext->HardwareStatus.TimeoutErrors++;
            break;

        case RS485_BUFFER_OVERFLOW:
            DeviceContext->HardwareStatus.BufferOverflows++;
            break;

        default:
            break;
    }
}

//
// Handle Buffer Overflow
//
VOID RS485HandleBufferOverflow(
    _In_ PRS485_DEVICE_CONTEXT DeviceContext,
    _In_ RS485_BUFFER_TYPE BufferType,
    _In_ const UINT8* PayloadData
)
{
    if (DeviceContext == NULL) {
        return;
    }

    RS485_WARNING_PRINT("Buffer overflow detected: Type=%d", BufferType);

    // Update statistics
    DeviceContext->HardwareStatus.BufferOverflows++;
    RS485UpdateErrorStatistics(&DeviceContext->ErrorStatistics, RS485_BUFFER_OVERFLOW);

    // Handle based on overflow policy
    switch (DeviceContext->OverflowPolicy) {
        case BufferOverflowTriggerError:
            // Default behavior - just log and return error
            RS485_ERROR_PRINT("Buffer overflow policy: Trigger error");
            break;

        case BufferOverflowDiscardOldest:
            // Remove oldest payload and add new one
            if (BufferType == BufferTypeUplink && DeviceContext->UplinkBuffer) {
                UINT8 discardedPayload[RS485_PAYLOAD_SIZE];
                if (NT_SUCCESS(RS485PopPayload(DeviceContext->UplinkBuffer, discardedPayload))) {
                    RS485PushPayload(DeviceContext->UplinkBuffer, PayloadData);
                    RS485_WARNING_PRINT("Discarded oldest uplink payload");
                }
            } else if (BufferType == BufferTypeDownlink && DeviceContext->DownlinkBuffer) {
                UINT8 discardedPayload[RS485_PAYLOAD_SIZE];
                if (NT_SUCCESS(RS485PopPayload(DeviceContext->DownlinkBuffer, discardedPayload))) {
                    RS485PushPayload(DeviceContext->DownlinkBuffer, PayloadData);
                    RS485_WARNING_PRINT("Discarded oldest downlink payload");
                }
            }
            break;

        case BufferOverflowDiscardNewest:
            // Simply discard the new payload
            RS485_WARNING_PRINT("Discarded newest payload due to buffer overflow");
            break;

        default:
            RS485_ERROR_PRINT("Unknown buffer overflow policy: %d", DeviceContext->OverflowPolicy);
            break;
    }

    // Update buffer flags
    RS485UpdateBufferFlags(DeviceContext);
}

//
// Calculate Frame CRC
//
UINT8 RS485CalculateFrameCRC(
    _In_ const RS485_FRAME* Frame
)
{
    if (Frame == NULL) {
        return 0;
    }

    // Calculate CRC for ID byte + payload (13 bytes total)
    return RS485CalculateCRC8(&Frame->IdByte, 13);
}

//
// Validate Frame
//
BOOLEAN RS485ValidateFrame(
    _In_ const RS485_FRAME* Frame
)
{
    if (Frame == NULL) {
        return FALSE;
    }

    // Check header and trailer
    if (Frame->Header != RS485_HEADER_BYTE || Frame->Trailer != RS485_TRAILER_BYTE) {
        return FALSE;
    }

    // Verify CRC
    if (!RS485VerifyCRC8(Frame)) {
        return FALSE;
    }

    // Validate function code (3 bits, so max value is 7)
    UINT8 functionCode = RS485_EXTRACT_FUNCTION_CODE(Frame->IdByte);
    if (functionCode > 7) {
        return FALSE;
    }

    // Validate device address (5 bits, so max value is 31)
    UINT8 deviceAddress = RS485_EXTRACT_DEVICE_ADDRESS(Frame->IdByte);
    if (deviceAddress > 31) {
        return FALSE;
    }

    return TRUE;
}

//
// Validate Payload
//
BOOLEAN RS485ValidatePayload(
    _In_ const RS485_PAYLOAD* Payload
)
{
    if (Payload == NULL) {
        return FALSE;
    }

    // Check if key contains valid ASCII characters (printable range)
    for (int i = 0; i < 4; i++) {
        if (Payload->Key[i] != 0 && (Payload->Key[i] < 0x20 || Payload->Key[i] > 0x7E)) {
            return FALSE;
        }
    }

    return TRUE;
}

//
// Work Item Callbacks (Placeholder implementations)
//
VOID RS485EvtReceiveWorkItem(
    _In_ WDFWORKITEM WorkItem
)
{
    PRS485_DEVICE_CONTEXT deviceContext = RS485GetDeviceContext(WdfWorkItemGetParentObject(WorkItem));
    
    RS485_DEBUG_PRINT("Receive work item executed");
    
    // TODO: Implement asynchronous receive processing
    // This would handle incoming data from the lower driver
    
    UNREFERENCED_PARAMETER(deviceContext);
}

VOID RS485EvtTransmitWorkItem(
    _In_ WDFWORKITEM WorkItem
)
{
    PRS485_DEVICE_CONTEXT deviceContext = RS485GetDeviceContext(WdfWorkItemGetParentObject(WorkItem));
    
    RS485_DEBUG_PRINT("Transmit work item executed");
    
    // TODO: Implement asynchronous transmit processing
    // This would handle outgoing data to the lower driver
    
    UNREFERENCED_PARAMETER(deviceContext);
}
