;
; RS485 Filter Driver Installation Information File
; Windows User-Mode Driver Framework (UMDF) Implementation
;

[Version]
Signature="$WINDOWS NT$"
Class=Ports
ClassGuid={4D36E978-E325-11CE-BFC1-08002BE10318}
Provider=%ManufacturerName%
CatalogFile=RS485FilterDriver.cat
DriverVer=01/10/2025,1.0.0.0
PnpLockdown=1

[Manufacturer]
%ManufacturerName%=Standard,NT$ARCH$

[Standard.NT$ARCH$]
%RS485Device.DeviceDesc%=RS485Filter_Install, USB\VID_0403&PID_6001

[RS485Filter_Install]
CopyFiles=UMDriverCopy
; This is a filter driver, so we don't replace the function driver
; Instead, we add ourselves as an upper filter

[RS485Filter_Install.Services]
AddService = , 0x00000002   ; null service install

[RS485Filter_Install.Wdf]
UmdfService=RS485FilterDriver,RS485Filter_Install
UmdfServiceOrder=RS485FilterDriver
; Configure as upper filter driver
UmdfKernelModeClientPolicy=AllowKernelModeClients
UmdfFileObjectPolicy=AllowNullAndUnknownFileObjects
UmdfFsContextUsePolicy=CanUseFsContext2

[RS485Filter_Install]
UmdfLibraryVersion=$UMDFVERSION$
ServiceBinary=%12%\UMDF\RS485FilterDriver.dll
; Filter driver specific settings
UmdfDispatcher=FileHandle
UmdfImpersonationLevel=Impersonation

[RS485Filter_Install.HW]
; Add this driver as an upper filter for FTDI devices
AddReg=RS485Filter_AddReg

[RS485Filter_AddReg]
HKR,,"UpperFilters",0x00010008,"RS485FilterDriver"

[UMDriverCopy]
RS485FilterDriver.dll

[DestinationDirs]
UMDriverCopy=12,UMDF

[Strings]
ManufacturerName="AI-SLDAP Technologies"
RS485Device.DeviceDesc="AI-SLDAP RS485 Filter Driver"
DiskName="RS485 Filter Driver Installation Disk"
